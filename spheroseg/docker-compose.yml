services:
  # Frontend development service with hot reload (commented out in favor of the static version)
  # frontend-dev:
  #   image: node:18-alpine
  #   container_name: spheroseg-frontend-dev
  #   restart: always
  #   depends_on:
  #     - backend
  #   ports:
  #     - "3000:3000"
  #   environment:
  #     # Use backend service name for container-to-container communication
  #     - VITE_API_URL=http://backend:5001
  #     - VITE_API_BASE_URL=
  #     - PORT=3000
  #     - VITE_NODE_ENV=development
  #     - HOST=0.0.0.0
  #     # Add standardized API path environment variables
  #     - VITE_API_AUTH_PREFIX=/auth
  #     - VITE_API_USERS_PREFIX=/users
  #     - VITE_API_PROXY_ENABLED=true
  #     - VITE_DOCKER_ENV=true
  #   volumes:
  #     - ./packages/frontend:/app
  #     - ./packages/shared:/app/shared
  #     - ./packages/types:/app/types
  #     - frontend_uploads:/app/public/uploads
  #   networks:
  #     - spheroseg-network
  #   working_dir: /app
  #   command: >
  #     sh -c "
  #       echo 'Starting frontend development server with hot reload...' &&
  #       echo 'VITE_API_URL=http://backend:5001' > .env.local &&
  #       echo 'VITE_API_BASE_URL=' >> .env.local &&
  #       echo 'VITE_API_AUTH_PREFIX=/auth' >> .env.local &&
  #       echo 'VITE_API_USERS_PREFIX=/users' >> .env.local &&
  #       echo 'VITE_API_PROXY_ENABLED=true' >> .env.local &&
  #       echo 'VITE_DOCKER_ENV=true' >> .env.local &&
  #       mkdir -p node_modules/@spheroseg &&
  #       rm -rf node_modules/@spheroseg/shared node_modules/@spheroseg/types &&
  #       ln -sf /app/shared node_modules/@spheroseg/shared &&
  #       ln -sf /app/types node_modules/@spheroseg/types &&
  #       npm install -g vite &&
  #       npm install --legacy-peer-deps &&
  #       npm run dev
  #     "

  # Database
  db:
    image: postgres:14-alpine
    container_name: spheroseg-db
    restart: always
    environment:
      POSTGRES_DB: spheroseg
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./packages/backend/src/db:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - spheroseg-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d spheroseg"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API using TypeScript server
  backend:
    image: python:3.9-slim
    container_name: spheroseg-backend
    restart: always
    depends_on:
      db:
        condition: service_healthy
    ports:
      - "5001:5001"
    environment:
      - PORT=5001
      - NODE_ENV=development
      - DATABASE_URL=**************************************/spheroseg
      - JWT_SECRET=development_secret_key
      - LOG_LEVEL=debug
      - ALLOW_CORS=true
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3003,http://localhost:3005,http://frontend:3000,http://frontend:80,*
      - DB_HOST=db
      - DB_PORT=5432
      - DB_NAME=spheroseg
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      - USE_MOCK_USER=true
      - PYTHONUNBUFFERED=1
      - MAX_UPLOAD_SIZE=100mb
      - VALIDATION_STRICT=false
      - DEBUG=true
    volumes:
      - ./packages/backend:/app
      - uploads_data:/app/uploads
      - ./packages/ml:/app/ML
      - backend_node_modules:/app/node_modules
    networks:
      - spheroseg-network
    working_dir: /app
    command: >
      bash -c "
        mkdir -p /app/uploads &&
        echo 'Starting server with hot reload...' &&
        apt-get update && apt-get install -y curl gnupg make g++ libpixman-1-dev libcairo2-dev libpango1.0-dev libjpeg-dev libgif-dev &&
        # Install OpenCV system dependencies
        apt-get install -y libgl1-mesa-glx libglib2.0-0 libsm6 libxrender1 libxext6 &&
        curl -fsSL https://deb.nodesource.com/setup_18.x | bash - &&
        apt-get install -y nodejs &&
        # Install Python dependencies for ML
        pip install --no-cache-dir torch torchvision numpy scikit-image opencv-python pillow tqdm &&
        # Install Node.js dependencies
        npm install --legacy-peer-deps --force &&
        npm install -g nodemon ts-node &&
        echo 'Initializing database schema...' &&
        node -r ts-node/register src/db/initDb.ts &&
        echo 'Creating test user...' &&
        node src/scripts/create-test-user.js &&
        echo 'Starting development server with hot reload using nodemon...' &&
        nodemon --watch src --ext js,ts,json --exec 'node -r ts-node/register src/server.ts' --verbose
      "

  # ML Service
  ml:
    build:
      context: ./packages/ml
      dockerfile: Dockerfile
    container_name: spheroseg-ml
    restart: always
    volumes:
      - ./packages/ml:/ML
      - uploads_data:/ML/uploads
    environment:
      - PYTHONUNBUFFERED=1
      - MODEL_PATH=/ML/checkpoint_epoch_9.pth.tar
      - DEBUG=true
      - ML_SERVICE_URL=http://ml:5002
    ports:
      - "5002:5002"
    networks:
      - spheroseg-network

  # Frontend with fixed configuration (commented out in favor of the static version)
  # frontend:
  #   build:
  #     context: ./packages/frontend
  #     dockerfile: Dockerfile.dev
  #   container_name: spheroseg-frontend
  #   restart: always
  #   depends_on:
  #     - backend
  #   ports:
  #     - "3000:3000"
  #   environment:
  #     # Use backend service name for container-to-container communication
  #     - VITE_API_URL=http://backend:5001
  #     - VITE_API_BASE_URL=
  #     - PORT=3000
  #     - VITE_NODE_ENV=development
  #     - HOST=0.0.0.0
  #     # Add standardized API path environment variables
  #     - VITE_API_AUTH_PREFIX=/auth
  #     - VITE_API_USERS_PREFIX=/users
  #     - VITE_API_PROXY_ENABLED=true
  #     - VITE_DOCKER_ENV=true
  #   volumes:
  #     - ./packages/frontend:/app
  #     - ./packages/shared:/app/shared
  #     - ./packages/types:/app/types
  #     - frontend_uploads:/app/public/uploads
  #     - frontend_node_modules:/app/node_modules
  #   networks:
  #     - spheroseg-network
  #   healthcheck:
  #     test: ["CMD", "wget", "-qO-", "http://localhost:3000"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 40s

  # NGINX reverse proxy for HTTPS
  nginx:
    image: nginx:alpine
    container_name: spheroseg-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      # Let's Encrypt certificates
      - ./letsencrypt/etc/letsencrypt:/etc/letsencrypt
      - ./letsencrypt/var/lib/letsencrypt:/var/lib/letsencrypt
      - ./letsencrypt/webroot:/var/www/letsencrypt
      # Fallback to self-signed certificates
      - ./ssl/server.crt:/etc/nginx/ssl/server.crt
      - ./ssl/server.key:/etc/nginx/ssl/server.key
    depends_on:
      - frontend-dev
      - backend
    command: >
      sh -c "
        mkdir -p /etc/nginx/ssl &&
        nginx -g 'daemon off;'
      "
    networks:
      - spheroseg-network
      
  # Certbot for SSL certificates
  certbot:
    image: certbot/certbot
    container_name: spheroseg-certbot
    volumes:
      - ./letsencrypt/etc/letsencrypt:/etc/letsencrypt
      - ./letsencrypt/var/lib/letsencrypt:/var/lib/letsencrypt
      - ./letsencrypt/webroot:/var/www/letsencrypt
    depends_on:
      - nginx
    command: "/bin/sh -c 'trap exit TERM; while :; do certbot renew --webroot -w /var/www/letsencrypt --quiet; if [ $? -eq 0 ]; then nginx -s reload; fi; sleep 12h & wait $${!}; done;'"
    networks:
      - spheroseg-network

  # Frontend development service with Vite server
  frontend-dev:
    build:
      context: ./packages/frontend
      dockerfile: Dockerfile.fixed
    container_name: spheroseg-frontend-dev
    restart: always
    depends_on:
      - backend
      - assets
    ports:
      - "3000:3000"
    environment:
      # Use backend service name for container-to-container communication
      - VITE_API_URL=http://backend:5001
      - VITE_API_BASE_URL=/api
      - PORT=3000
      - VITE_NODE_ENV=development
      - HOST=0.0.0.0
      # Add standardized API path environment variables
      - VITE_API_AUTH_PREFIX=/auth
      - VITE_API_USERS_PREFIX=/users
      - VITE_API_PROXY_ENABLED=true
      - VITE_DOCKER_ENV=true
      - VITE_ASSETS_URL=http://assets
    volumes:
      - ./packages/frontend:/app:cached
      - ./packages/shared:/app/shared:cached
      - ./packages/types:/app/types:cached
      - frontend_node_modules:/app/node_modules
    networks:
      - spheroseg-network
    working_dir: /app

  # Static assets server
  assets:
    image: nginx:alpine
    container_name: spheroseg-assets
    restart: always
    ports:
      - "3001:80"
    volumes:
      - ./packages/frontend-static:/usr/share/nginx/html
    networks:
      - spheroseg-network

  # Database management UI
  adminer:
    image: adminer:latest
    container_name: spheroseg-adminer
    restart: always
    ports:
      - "8081:8080"
    depends_on:
      - db
    networks:
      - spheroseg-network

volumes:
  postgres_data:
  uploads_data:
  frontend_uploads:
  frontend_node_modules:
  backend_node_modules:

networks:
  spheroseg-network:
    driver: bridge
