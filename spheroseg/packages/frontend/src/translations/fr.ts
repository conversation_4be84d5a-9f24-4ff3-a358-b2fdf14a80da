export default {
  project: {
    detail: {
      noImagesSelected: 'Aucune image sélectionnée',
      triggeringResegmentation: 'Déclenchement de la re-segmentation pour {{count}} images...',
      deleteConfirmation: 'Êtes-vous sûr de vouloir supprimer {{count}} images ? Cette action ne peut pas être annulée.',
      deletingImages: 'Suppression de {{count}} images...',
      deleteSuccess: '{{count}} images supprimées avec succès',
      deleteFailed: 'Échec de la suppression de {{count}} images',
      preparingExport: 'Préparation de l\'exportation de {{count}} images...'
    },
    segmentation: {
      processingInBatches: 'Démarrage de la segmentation pour {{count}} images en {{batches}} lots...',
      batchQueued: 'Lot {{current}}/{{total}} mis en file d\'attente avec succès',
      batchQueuedFallback: 'Lot {{current}}/{{total}} mis en file d\'attente avec succès (endpoint de secours)',
      batchError: 'Erreur lors du traitement du lot {{current}}/{{total}}',
      partialSuccess: 'Segmentation : {{success}} images mises en file d\'attente avec succès, {{failed}} échecs',
      allSuccess: 'Segmentation : Toutes les {{count}} images mises en file d\'attente avec succès',
      allFailed: 'Segmentation : Toutes les {{count}} images ont échoué',
      startedImages: 'Segmentation démarrée pour {{count}} images',
      queuedLocallyWarning: 'Segmentation mise en file d\'attente localement pour {{count}} images. La connexion au serveur a échoué.'
    },
  },
  common: {
    appName: 'Segmentation de Sphéroïdes',
    appNameShort: 'SpheroSeg',
    loading: 'Chargement...',
    save: 'Enregistrer',
    cancel: 'Annuler',
    delete: 'Supprimer',
    edit: 'Modifier',
    create: 'Créer',
    search: 'Rechercher',
    error: 'Erreur',
    success: 'Succès',
    back: 'Retour',
    signIn: 'Se connecter',
    signUp: "S'inscrire",
    signOut: 'Se déconnecter',
    settings: 'Paramètres',
    profile: 'Profil',
    dashboard: 'Tableau de bord',
    project: 'Projet',
    projects: 'Projets',
    newProject: 'Nouveau projet',
    upload: 'Télécharger',
    uploadImages: 'Télécharger des images',
    recentAnalyses: 'Analyses récentes',
    noProjects: 'Aucun projet trouvé',
    noImages: 'Aucune image trouvée',
    createYourFirst: 'Créez votre premier projet pour commencer',
    tryAgain: 'Réessayer',
    email: 'Email',
    password: 'Mot de passe',
    name: 'Nom',
    description: 'Description',
    date: 'Date',
    status: 'Statut',
    images: 'Images',
    image: 'Image',
    projectName: 'Nom du projet',
    projectDescription: 'Description du projet',
    theme: 'Thème',
    language: 'Langue',
    light: 'Clair',
    dark: 'Sombre',
    system: 'Système',
    welcome: 'Bienvenue sur la plateforme de segmentation de sphéroïdes',
    account: 'Compte',
    notifications: 'Notifications',
    passwordConfirm: 'Confirmer le mot de passe',
    manageAccount: 'Gérer votre compte',
    changePassword: 'Changer le mot de passe',
    deleteAccount: 'Supprimer le compte',
    requestAccess: 'Demander un accès',
    termsOfService: "Conditions d'utilisation",
    privacyPolicy: 'Politique de confidentialité',
    accessRequest: "Demande d'accès",
    createAccount: 'Créer un compte',
    signInToAccount: 'Se connecter à votre compte',
    sort: 'Trier',
    backToHome: 'Back to Home',
    termsOfServiceLink: 'Terms of Service',
    privacyPolicyLink: 'Privacy Policy',
    optional: 'Optionnel',
    saveChanges: 'Enregistrer les modifications',
    saving: 'Enregistrement',
    notSpecified: 'Non spécifié',
    no: 'Non',
    profileTitle: 'Profil',
    profileDescription: 'Mettez à jour les informations de votre profil visibles par les autres utilisateurs',
    profileUsername: "Nom d'utilisateur",
    profileUsernamePlaceholder: "Entrez votre nom d'utilisateur",
    profileFullName: 'Nom complet',
    profileFullNamePlaceholder: 'Entrez votre nom complet',
    profileTitlePlaceholder: 'ex. Chercheur scientifique, Professeur',
    profileOrganization: 'Organisation',
    profileOrganizationPlaceholder: 'Entrez votre organisation ou institution',
    profileBio: 'Biographie',
    profileBioPlaceholder: 'Écrivez une courte biographie sur vous-même',
    profileBioDescription: 'Brève description de vos intérêts de recherche et de votre expertise',
    profileLocation: 'Emplacement',
    profileLocationPlaceholder: 'ex. Paris, France',
    profileSaveButton: 'Enregistrer le profil',
    reset: 'Réinitialiser',
    clear: 'Effacer',
    download: 'Télécharger',
    removeAll: 'Tout supprimer',
    confirmPassword: 'Confirmer le mot de passe',
    firstName: 'Prénom',
    lastName: 'Nom de famille',
    username: "Nom d'utilisateur",
    enable: 'Activer',
    disable: 'Désactiver',
    and: 'et',
    lastChange: 'Dernière modification',
    emailPlaceholder: 'Entrez votre email',
    passwordPlaceholder: 'Entrez votre mot de passe',
    export: 'Exporter',
    selectImages: 'Sélectionner des images',
    noImagesDescription: 'Téléchargez des images pour commencer votre projet',
    yes: 'Oui',
    editor: {
      error: 'Erreur',
      success: 'Succès',
      edit: 'Modifier',
      create: 'Créer',
    },
  },
  requestAccess: {
    title: "Demander l'accès à la plateforme de segmentation de sphéroïdes",
    description:
      "Remplissez le formulaire ci-dessous pour demander l'accès à notre plateforme. Nous examinerons votre demande et vous contacterons bientôt.",
    emailLabel: 'Votre adresse email',
    nameLabel: 'Votre nom',
    institutionLabel: 'Institution/Entreprise',
    reasonLabel: "Raison de l'accès",
    submitRequest: 'Soumettre la demande',
    requestReceived: 'Demande reçue',
    thankYou: 'Merci pour votre intérêt',
    weWillContact: 'Nous examinerons votre demande et vous contacterons bientôt',
    submitSuccess: 'Demande soumise avec succès!',
    and: 'et',
  },
  documentation: {
    tag: "Guide d'utilisation",
    title: 'Documentation SpheroSeg',
    subtitle: 'Apprenez à utiliser efficacement la plateforme de segmentation des sphéroïdes.',
    sidebar: {
      title: 'Sections',
      introduction: 'Introduction',
      gettingStarted: 'Démarrage',
      uploadingImages: "Téléchargement d'images",
      segmentationProcess: 'Processus de segmentation',
      apiReference: "Référence de l'API",
    },
    introduction: {
      title: 'Introduction',
      imageAlt: '[fr] Illustration of spheroid analysis workflow',
      whatIs: {
        title: "Qu'est-ce que SpheroSeg?",
        paragraph1: 'SpheroSeg est une plateforme de pointe conçue pour...',
        paragraph2: "Elle utilise des algorithmes d'IA avancés pour...",
        paragraph3: 'Cette documentation vous guidera à travers...',
      },
    },
    gettingStarted: {
      title: 'Démarrage',
      accountCreation: {
        title: '[fr] Account Creation',
        paragraph1: "Pour commencer à utiliser SpheroSeg, vous avez besoin d'un compte...",
        step1Prefix: 'Naviguez vers la',
        step1Link: "page de demande d'accès",
        step2: 'Remplissez les détails requis.',
        step3: 'Soumettez le formulaire.',
        step4: "Attendez l'approbation de l'administrateur.",
      },
      creatingProject: {
        title: '[fr] Creating Your First Project',
        paragraph1: 'Les projets vous aident à organiser vos images et analyses...',
        step1: 'Connectez-vous à votre compte.',
        step2: 'Allez au tableau de bord.',
        step3: 'Cliquez sur "Nouveau projet".',
        step4: 'Entrez un nom et une description pour votre projet.',
      },
    },
    uploadingImages: {
      title: "Téléchargement d'images",
      paragraph1: 'Une fois que vous avez un projet, vous pouvez télécharger vos images de microscopie...',
      methods: {
        title: '[fr] Upload Methods',
        paragraph1: 'Vous pouvez télécharger des images en utilisant la méthode suivante:',
        step1: 'Naviguez vers votre projet.',
        step2: 'Cliquez sur le bouton "Télécharger des images".',
        step3: 'Glissez et déposez vos fichiers ou cliquez pour les sélectionner.',
      },
      note: {
        prefix: 'Remarque:',
        text: 'Assurez-vous que vos images sont dans des formats acceptés (JPEG, PNG, TIFF, BMP) et dans la limite de taille.',
      },
    },
    segmentationProcess: {
      title: 'Processus de segmentation',
      paragraph1: 'SpheroSeg propose des outils de segmentation automatiques et manuels...',
      automatic: {
        title: '[fr] Automatic Segmentation',
        paragraph1: "Notre modèle d'IA peut segmenter automatiquement les sphéroïdes...",
        step1: 'Sélectionnez les images que vous souhaitez analyser.',
        step2: 'Choisissez le modèle de segmentation souhaité (si applicable).',
        step3: "Démarrez le processus d'analyse.",
        step4: 'Les résultats apparaîtront une fois le traitement terminé.',
      },
      manual: {
        title: '[fr] Manual Editing Tools',
        paragraph1: 'Vous pouvez affiner les segmentations automatiques ou en créer de nouvelles manuellement...',
        step1: "Ouvrez une image dans l'éditeur.",
        step2: "Sélectionnez l'outil approprié (par exemple, Ajouter un polygone, Éditer les sommets).",
        step3: "Faites vos ajustements sur le canevas de l'image.",
        step4: 'Enregistrez vos modifications régulièrement.',
      },
    },
    apiReference: {
      title: "Référence de l'API",
      paragraph1: 'Pour les utilisateurs avancés, SpheroSeg fournit une API REST...',
      endpoint1Desc: 'Récupérez une liste de vos projets.',
      endpoint2Desc: 'Obtenez des images dans un projet spécifique.',
      endpoint3Desc: 'Initiez la segmentation pour une image spécifique.',
      contactPrefix: '<EMAIL>',
    },
    backToHome: "Retour à l'accueil",
    backToTop: 'Retour en haut',
  },
  dashboard: {
    manageProjects: 'Gérez vos projets de recherche et analyses',
    statsOverview: 'Aperçu des statistiques',
    totalProjects: 'Total des projets',
    activeProjects: 'Projets actifs',
    totalImages: "Total d'images",
    totalAnalyses: "Total d'analyses",
    lastUpdated: 'Dernière mise à jour',
    noProjectsDescription: "Vous n'avez pas encore créé de projet. Créez votre premier projet pour commencer.",
    noImagesDescription: 'Téléchargez quelques images pour commencer',
    searchProjectsPlaceholder: 'Rechercher des projets...',
    searchImagesPlaceholder: 'Rechercher des images par nom...',
    sortBy: 'Trier par',
    name: 'Nom',
    lastChange: 'Dernière modification',
    status: 'Statut',
    completed: 'Terminé',
    processing: 'En cours',
    pending: 'En attente',
    failed: 'Échoué',
    selectImagesButton: 'Select Images',
    viewMode: {
      grid: 'Vue en grille',
      list: 'Vue en liste',
    },
    sort: {
      name: 'Nom',
      updatedAt: 'Dernière mise à jour',
      segmentationStatus: 'Statut',
    },
    search: 'Rechercher des projets...',
    noProjects: 'Aucun projet trouvé',
    createFirst: 'Créez votre premier projet pour commencer',
    createNew: 'Créer un nouveau projet',
  },
  projects: {
    createProject: 'Créer un nouveau projet',
    createProjectDesc: 'Ajoutez un nouveau projet pour organiser vos images de sphéroïdes et vos analyses.',
    projectNamePlaceholder: 'ex., Sphéroïdes de cellules HeLa',
    projectDescPlaceholder: 'ex., Analyse de sphéroïdes tumoraux pour des études de résistance aux médicaments',
    creatingProject: 'Création en cours...',
    duplicateProject: 'Dupliquer',
    shareProject: 'Partager',
    deleteProject: 'Supprimer',
    openProject: 'Ouvrir le projet',
    confirmDelete: 'Êtes-vous sûr de vouloir supprimer ce projet?',
    projectCreated: 'Projet créé avec succès',
    projectDeleted: 'Projet supprimé avec succès',
    viewProject: 'Voir le projet',
    projectImages: 'Images du projet',
    projectSelection: 'Sélection de projet',
    selectProject: 'Sélectionnez un projet',
    projectNameRequired: 'Veuillez entrer un nom de projet',
    loginRequired: 'Vous devez être connecté pour créer un projet',
    createSuccess: 'Projet créé avec succès',
    createError: 'Échec de la création du projet',
    invalidData: 'Données de projet invalides',
    title: 'Projets',
    description: 'Gérez vos projets de recherche',
    createNew: 'Créer un nouveau projet',
    projectName: 'Nom du projet',
    projectDescription: 'Description du projet',
    projectDescriptionPlaceholder: 'Entrez la description du projet',
    projectCreationFailed: 'Échec de la création du projet',
    projectDeletionFailed: 'Échec de la suppression du projet',
    confirmDeleteDescription:
      'Cette action ne peut pas être annulée. Toutes les données associées à ce projet seront définitivement supprimées.',
    editProject: 'Modifier le projet',
    projectUpdated: 'Projet mis à jour avec succès',
    projectUpdateFailed: 'Échec de la mise à jour du projet',
    noProjects: 'Aucun projet trouvé',
    createFirstProject: 'Créez votre premier projet pour commencer',
    searchProjects: 'Rechercher des projets...',
    filterProjects: 'Filtrer les projets',
    sortProjects: 'Trier les projets',
    createdAt: 'Créé',
    updatedAt: 'Dernière mise à jour',
    imageCount: 'Images',
    status: 'Statut',
    actions: 'Actions',
    loading: 'Chargement des projets...',
    error: 'Erreur lors du chargement des projets',
    retry: 'Réessayer',
  },
  projectActions: {
    duplicateTooltip: 'Dupliquer le projet',
    deleteTooltip: 'Supprimer le projet',
    deleteConfirmTitle: 'Êtes-vous sûr(e)?',
    deleteConfirmDesc: 'Voulez-vous vraiment supprimer le projet "{{projectName}}"? Cette action est irréversible.',
    deleteSuccess: 'Projet "{{projectName}}" supprimé avec succès.',
    deleteError: 'Échec de la suppression du projet.',
    duplicateSuccess: 'Projet "{{projectName}}" dupliqué avec succès.',
    duplicateError: 'Échec de la duplication du projet.',
    makePrivateTooltip: 'Rendre privé',
    makePublicTooltip: 'Rendre public',
    shareTooltip: 'Partager le projet',
    downloadTooltip: 'Télécharger le projet',
    notFound: 'Le projet "{{projectName}}" n\'a pas été trouvé. Il a peut-être déjà été supprimé.',
  },
  projectToolbar: {
    selectImages: 'Select Images',
    cancelSelection: 'Cancel Selection',
    export: 'Export',
    uploadImages: 'Télécharger des images',
  },
  images: {
    uploadImages: 'Télécharger des images',
    dragDrop: 'Glissez et déposez des images ici',
    clickToSelect: 'ou cliquez pour sélectionner des fichiers',
    acceptedFormats: 'Formats acceptés: JPEG, PNG, TIFF, BMP (max 10MB)',
    uploadProgress: 'Progression du téléchargement',
    uploadingTo: "Sélectionnez d'abord un projet",
    currentProject: 'projet actuel',
    autoSegment: 'Segmentation automatique après téléchargement',
    uploadCompleted: 'Téléchargement terminé',
    uploadFailed: 'Échec du téléchargement',
    imagesUploaded: 'Images téléchargées avec succès',
    imagesFailed: 'Échec du téléchargement des images',
    viewAnalyses: 'Voir les analyses',
    noAnalysesYet: "Pas encore d'analyses",
    runAnalysis: "Lancer l'analyse",
    viewResults: 'Voir les résultats',
    dropImagesHere: 'Déposez les images ici...',
    selectProjectFirst: "Veuillez d'abord sélectionner un projet",
    projectRequired: 'Vous devez sélectionner un projet avant de pouvoir télécharger des images',
    imageOnly: '(Fichiers image uniquement)',
    dropFiles: 'Déposez les fichiers ici...',
    filesToUpload: 'Fichiers à télécharger ({{count}})',
    uploadBtn: 'Télécharger {{count}} image(s)',
    uploadError: "Une erreur s'est produite lors du téléchargement. Veuillez réessayer.",
    noProjectsToUpload: "Aucun projet disponible. Créez d'abord un projet.",
    notFound: 'Le projet "{{projectName}}" n\'a pas été trouvé. Il a peut-être déjà été supprimé.',
  },
  settings: {
    manageSettings: 'Gérez les préférences de votre compte',
    appearance: 'Apparence',
    themeSettings: 'Paramètres de thème',
    systemDefault: 'Par défaut du système',
    languageSettings: 'Paramètres de langue',
    selectLanguage: 'Sélectionner une langue',
    changeLanguage: 'Changer de langue',
    useBrowserLanguage: 'Utiliser la langue du navigateur',
    accountSettings: 'Paramètres du compte',
    notificationSettings: 'Paramètres de notification',
    emailNotifications: 'Notifications par email',
    pushNotifications: 'Notifications push',
    profileSettings: 'Paramètres du profil',
    profileUpdated: 'Profil mis à jour avec succès',
    profileUpdateFailed: 'Échec de la mise à jour du profil',
    saveChanges: 'Enregistrer les modifications',
    savingChanges: 'Enregistrement des modifications...',
    notifications: {
      projectUpdates: 'Mises à jour de projet',
      analysisCompleted: 'Analyse terminée',
      newFeatures: 'Nouvelles fonctionnalités',
      marketingEmails: 'Emails marketing',
      billing: 'Notifications de facturation',
    },
    pageTitle: 'Paramètres',
    profile: 'Profil',
    account: 'Compte',
    profileTitle: 'Informations du profil',
    profileDescription: 'Mettez à jour les informations de votre profil visibles par les autres utilisateurs',
    username: "Nom d'utilisateur",
    usernamePlaceholder: "Entrez votre nom d'utilisateur",
    fullNamePlaceholder: 'Entrez votre nom complet',
    title: 'Titre',
    titlePlaceholder: 'ex. Chercheur scientifique, Professeur',
    organizationPlaceholder: 'Entrez votre organisation ou institution',
    bio: 'Bio',
    bioPlaceholder: 'Écrivez une courte biographie sur vous-même',
    bioDescription: 'Brève description de vos intérêts de recherche et de votre expertise',
    location: 'Emplacement',
    locationPlaceholder: 'ex. Paris, France',
    fetchError: 'Erreur lors de la récupération des données du profil',
    updateSuccess: 'Profil mis à jour avec succès',
    updateError: 'Échec de la mise à jour du profil',
    noChanges: 'Aucune modification à enregistrer',
    profileLoadError: 'Échec du chargement des données du profil',
    languageUpdated: 'Langue mise à jour avec succès',
    themeUpdated: 'Thème mis à jour avec succès',
    appearanceDescription: "Personnalisez l'apparence de l'application",
    languageDescription: 'Sélectionnez votre langue préférée',
    notificationsTab: 'Notifications',
    personal: 'Informations personnelles',
    fullName: 'Nom complet',
    organization: 'Organisation',
    department: 'Département',
    publicProfile: 'Profil public',
    makeProfileVisible: 'Rendre mon profil visible pour les autres chercheurs',
    dangerZone: 'Zone de danger',
    deleteAccountWarning:
      "Une fois que vous supprimez votre compte, il n'y a pas de retour en arrière. Toutes vos données seront supprimées de façon permanente.",
    currentPassword: 'Mot de passe actuel',
    newPassword: 'Nouveau mot de passe',
    confirmNewPassword: 'Confirmer le nouveau mot de passe',
    securitySettings: 'Paramètres de sécurité',
    preferenceSettings: 'Paramètres de préférence',
    selectTheme: 'Sélectionner un thème',
    updateProfile: 'Mettre à jour le profil',
    changePassword: 'Changer le mot de passe',
    deleteAccount: 'Supprimer le compte',
    savedChanges: 'Modifications enregistrées avec succès',
    privacySettings: 'Paramètres de confidentialité',
    exportData: 'Exporter les données',
    importData: 'Importer les données',
    uploadAvatar: 'Télécharger une photo de profil',
    removeAvatar: 'Supprimer la photo de profil',
    twoFactorAuth: 'Authentification à deux facteurs',
    weeklyDigest: 'Résumé hebdomadaire',
    monthlyReport: 'Rapport mensuel',
    displaySettings: "Paramètres d'affichage",
    accessibilitySettings: "Paramètres d'accessibilité",
    advancedSettings: 'Paramètres avancés',
    language: 'Langue',
    theme: 'Thème',
    light: 'Clair',
    dark: 'Sombre',
    system: 'Système',
    themeDescription: 'Choisissez votre thème préféré',
  },
  auth: {
    signIn: 'Se connecter',
    signUp: "S'inscrire",
    signOut: 'Se déconnecter',
    forgotPassword: 'Mot de passe oublié?',
    resetPassword: 'Réinitialiser le mot de passe',
    dontHaveAccount: "Vous n'avez pas de compte?",
    alreadyHaveAccount: 'Vous avez déjà un compte?',
    signInWith: 'Se connecter avec',
    signUpWith: "S'inscrire avec",
    orContinueWith: 'ou continuer avec',
    rememberMe: 'Se souvenir de moi',
    emailRequired: "L'email est requis",
    passwordRequired: 'Le mot de passe est requis',
    invalidEmail: 'Adresse email invalide',
    passwordTooShort: 'Le mot de passe doit comporter au moins 6 caractères',
    passwordsDontMatch: 'Les mots de passe ne correspondent pas',
    mustAgreeTerms: 'Vous devez accepter les conditions générales',
    signUpSuccessEmail:
      "Inscription réussie ! Veuillez vérifier votre e-mail ou attendre l'approbation de l'administrateur.",
    signUpFailed: "Échec de l'inscription. Veuillez réessayer.",
    successfulSignIn: 'Connexion réussie',
    successfulSignUp: 'Inscription réussie',
    verifyEmail: 'Veuillez vérifier votre email pour confirmer votre compte',
    successfulSignOut: 'Déconnexion réussie',
    checkingAuthentication: "Vérification de l'authentification...",
    loadingAccount: 'Chargement de votre compte...',
    processingRequest: 'Traitement de votre demande...',
    firstNamePlaceholder: 'ex. Max',
    lastNamePlaceholder: 'ex. Dupont',
    emailPlaceholder: '<EMAIL>',
    passwordPlaceholder: '••••••••',
    alreadyLoggedInTitle: 'Vous êtes déjà connecté',
    alreadyLoggedInMessage: 'Vous êtes déjà connecté à votre compte.',
    goToDashboardLink: 'Aller au tableau de bord',
    emailAddressLabel: 'Adresse e-mail',
    passwordLabel: 'Mot de passe',
    signingIn: 'Connexion en cours...',
    fillAllFields: 'Veuillez remplir tous les champs',
    signInToAccount: 'Connectez-vous à votre compte',
    accessPlatform: 'Accédez à la plateforme de segmentation des sphéroïdes',
    requestAccess: "Demander l'accès",
    termsAndPrivacy:
      "En vous connectant, vous acceptez nos Conditions d'utilisation et notre Politique de confidentialité",
    createAccount: 'Créer un compte',
    signInWithGoogle: 'Se connecter avec Google',
    signInWithGithub: 'Se connecter avec GitHub',
    or: 'ou',
    signInTitle: 'Se connecter',
    signInDescription: 'Connectez-vous à votre compte',
    noAccount: "Vous n'avez pas de compte?",
    currentPasswordLabel: 'Mot de passe actuel',
    newPasswordLabel: 'Nouveau mot de passe',
    confirmPasswordLabel: 'Confirmer le mot de passe',
    invalidCredentials: 'Email ou mot de passe invalide',
    accountCreated: 'Compte créé avec succès',
    resetLinkSent: 'Lien de réinitialisation du mot de passe envoyé à votre email',
    resetSuccess: 'Mot de passe réinitialisé avec succès',
    signInSuccess: 'Connecté avec succès',
    signOutSuccess: 'Déconnecté avec succès',
    sessionExpired: 'Votre session a expiré. Veuillez vous reconnecter.',
    unauthorized: "Vous n'êtes pas autorisé à accéder à cette ressource",
    verificationLinkSent: 'Lien de vérification envoyé à votre email',
    verificationSuccess: 'Email vérifié avec succès',
    resendVerification: "Renvoyer l'email de vérification",
    forgotPasswordLink: 'Mot de passe oublié?',
    passwordChanged: 'Mot de passe changé avec succès',
    currentPasswordIncorrect: 'Le mot de passe actuel est incorrect',
    registerTitle: 'Créer un compte',
    registerDescription: 'Inscrivez-vous pour un nouveau compte',
    registerSuccess: 'Inscription réussie! Vous pouvez maintenant vous connecter.',
    forgotPasswordTitle: 'Réinitialiser votre mot de passe',
    checkYourEmail: 'Vérifiez votre e-mail pour obtenir un lien de réinitialisation',
    enterEmailForReset: 'Entrez votre adresse e-mail et nous vous enverrons un lien de réinitialisation',
    passwordResetLinkSent: 'Si un compte existe pour cet e-mail, un lien de réinitialisation a été envoyé',
    passwordResetFailed: "Échec de l'envoi du lien de réinitialisation. Veuillez réessayer.",
    enterEmail: 'Veuillez entrer votre adresse e-mail',
    sendingResetLink: 'Envoi du lien de réinitialisation...',
    sendResetLink: 'Envoyer le lien de réinitialisation',
    backToSignIn: 'Retour à la connexion',
  },
  requestAccessForm: {
    title: "Demander l'accès à la plateforme de segmentation des sphéroïdes",
    description:
      "Remplissez le formulaire ci-dessous pour demander l'accès à notre plateforme. Nous examinerons votre demande et vous contacterons bientôt.",
    emailLabel: 'Votre adresse email',
    nameLabel: 'Votre nom',
    institutionLabel: 'Institution/Entreprise',
    reasonLabel: "Raison de l'accès",
    submitButton: 'Soumettre la demande',
    signInPrompt: 'Vous avez déjà un compte?',
    signInLink: 'Se connecter',
    thankYouTitle: 'Merci pour votre intérêt',
    weWillContact: 'Nous examinerons votre demande et vous contacterons bientôt',
    agreeToTerms: 'En soumettant cette demande, vous acceptez nos',
    and: 'et',
  },
  profile: {
    title: 'Titre',
    about: 'À propos',
    activity: 'Activité',
    projects: 'Projets',
    recentProjects: 'Projets récents',
    recentAnalyses: 'Analyses récentes',
    accountDetails: 'Détails du compte',
    accountType: 'Type de compte',
    joinDate: "Date d'inscription",
    lastActive: 'Dernière activité',
    projectsCreated: 'Projets créés',
    imagesUploaded: 'Images téléchargées',
    segmentationsCompleted: 'Segmentations terminées',
    pageTitle: 'Profil utilisateur',
    editProfile: 'Modifier le profil',
    joined: 'Inscrit',
    statistics: 'Statistiques',
    images: 'Images',
    analyses: 'Analyses',
    storageUsed: 'Espace de stockage utilisé',
    recentActivity: 'Activité récente',
    noRecentActivity: 'Aucune activité récente',
    fetchError: 'Échec de la récupération des données du profil',
    aboutMe: 'À propos de moi',
    noBio: 'Aucune biographie fournie',
    avatarHelp: "Cliquez sur l'icône de caméra pour télécharger une photo de profil",
    avatarImageOnly: 'Veuillez sélectionner un fichier image',
    avatarTooLarge: "L'image doit être inférieure à 5MB",
    avatarUpdated: 'Photo de profil mise à jour',
    avatarUploadError: 'Échec du téléchargement de la photo de profil',
    avatarRemoved: 'Photo de profil supprimée',
    avatarRemoveError: 'Échec de la suppression de la photo de profil',
    description: 'Mettez à jour vos informations personnelles et votre photo de profil',
    saveButton: 'Enregistrer le profil',
    username: "Nom d'utilisateur",
    usernamePlaceholder: "Entrez votre nom d'utilisateur",
    fullName: 'Nom complet',
    fullNamePlaceholder: 'Entrez votre nom complet',
    titlePlaceholder: 'Entrez votre titre ou poste',
    organization: 'Organisation',
    organizationPlaceholder: 'Entrez votre organisation ou entreprise',
    bio: 'Biographie',
    bioPlaceholder: 'Parlez-nous de vous',
    bioDescription: 'Une brève description de vous-même qui sera visible sur votre profil',
    location: 'Emplacement',
    locationPlaceholder: 'Entrez votre emplacement',
  },
  hero: {
    platformTag: 'Plateforme avancée de segmentation des sphéroïdes',
    title: "Analyse cellulaire basée sur l'IA pour la recherche biomédicale",
    subtitle:
      "Améliorez votre analyse d'images cellulaires microscopiques avec notre plateforme de pointe pour la segmentation des sphéroïdes. Conçue pour les chercheurs en quête de précision et d'efficacité.",
    getStartedButton: 'Commencer',
    learnMoreButton: 'En savoir plus',
    imageAlt1: 'Image microscopique de sphéroïde',
    imageAlt2: 'Image microscopique de sphéroïde avec analyse',
    welcomeTitle: 'Bienvenue sur SpheroSeg',
    welcomeSubtitle: "Plateforme avancée pour la segmentation et l'analyse des sphéroïdes cellulaires",
    welcomeDescription:
      "Notre plateforme combine des algorithmes d'intelligence artificielle de pointe avec une interface intuitive pour une détection et une analyse précises des sphéroïdes cellulaires dans les images microscopiques.",
    featuresTitle: 'Capacités puissantes',
    featuresSubtitle: 'Outils avancés pour la recherche biomédicale',
    featureAiSegmentation: 'Segmentation avancée',
    featureAiSegmentationDesc:
      'Détection précise des sphéroïdes avec analyse des contours pour des mesures cellulaires précises.',
    featureEditing: "Analyse basée sur l'IA",
    featureEditingDesc:
      "Exploitez les algorithmes d'apprentissage profond pour la détection et la classification automatisées des cellules.",
    featureAnalytics: 'Téléchargements sans effort',
    featureAnalyticsDesc: 'Glissez et déposez vos images microscopiques pour un traitement et une analyse instantanés.',
    featureExport: 'Aperçus statistiques',
    featureExportDesc: 'Métriques et visualisations complètes pour extraire des modèles de données significatifs.',
    ctaTitle: "Prêt à transformer votre flux de travail d'analyse cellulaire?",
    ctaSubtitle:
      'Rejoignez les chercheurs de premier plan qui utilisent déjà notre plateforme pour accélérer leurs découvertes.',
    ctaButton: 'Créer un compte',
  },
  navbar: {
    home: 'Accueil',
    features: 'Fonctionnalités',
    documentation: 'Documentation',
    terms: 'Conditions',
    privacy: 'Confidentialité',
    login: 'Connexion',
    requestAccess: "Demander l'accès",
  },
  footer: {
    developerName: 'Bc. Michal Průšek',
    facultyName: 'FJFI ČVUT v Praze',
    copyrightNotice: '© 2025 SpheroSeg.',
    description: "Plateforme avancée pour la segmentation et l'analyse des sphéroïdes cellulaires",
    contactLabel: '<EMAIL>',
    developerLabel: 'Bc. Michal Průšek',
    facultyLabel: 'FJFI ČVUT à Prague',
    resourcesTitle: 'Ressources',
    documentationLink: 'Documentation',
    featuresLink: 'Fonctionnalités',
    tutorialsLink: 'Tutoriels',
    researchLink: 'Recherche',
    legalTitle: 'Mentions légales',
    termsLink: "Conditions d'utilisation",
    privacyLink: 'Confidentialité',
    contactUsLink: 'Contactez-nous',
    informationTitle: 'Information',
    contactTitle: 'Contact',
  },
  features: {
    tag: 'Fonctionnalités',
    title: 'Découvrez les capacités de notre plateforme',
    subtitle: 'Outils avancés pour la recherche biomédicale',
    cards: {
      segmentation: {
        title: 'Segmentation avancée',
        description: 'Détection précise des sphéroïdes avec analyse des contours pour des mesures cellulaires exactes',
      },
      aiAnalysis: {
        title: 'Analyse par IA',
        description:
          "Utilisez des algorithmes d'apprentissage profond pour la détection et la classification automatisées des cellules",
      },
      uploads: {
        title: 'Téléchargement facile',
        description: 'Glissez-déposez vos images microscopiques pour un traitement et une analyse immédiats',
      },
      insights: {
        title: 'Aperçus statistiques',
        description: 'Métriques et visualisations complètes pour extraire des modèles de données significatifs',
      },
      collaboration: {
        title: "Collaboration d'équipe",
        description: 'Partagez des projets et des résultats avec des collègues pour une recherche plus efficace',
      },
      pipeline: {
        title: 'Processus automatisé',
        description: 'Simplifiez votre flux de travail avec nos outils de traitement par lots',
      },
    },
  },
  index: {
    about: {
      tag: 'À propos de la plateforme',
      title: "Qu'est-ce que SpheroSeg ?",
      imageAlt: 'Exemple de segmentation de sphéroïde',
      paragraph1:
        "SpheroSeg est une plateforme avancée conçue spécifiquement pour la segmentation et l'analyse des sphéroïdes cellulaires dans les images microscopiques.",
      paragraph2:
        "Notre outil combine des algorithmes d'intelligence artificielle de pointe avec une interface intuitive pour fournir aux chercheurs une détection précise des contours des sphéroïdes et des capacités analytiques.",
      paragraph3:
        "La plateforme a été développée par Michal Průšek de FNSPE CTU à Prague sous la supervision d'Adam Novozámský de l'UTIA CAS, en collaboration avec des chercheurs du Département de Biochimie et Microbiologie de l'UCT Prague.",
      contactPrefix: '<EMAIL>',
    },
    cta: {
      title: 'Prêt à transformer votre recherche ?',
      subtitle:
        "Commencez à utiliser SpheroSeg aujourd'hui et découvrez de nouvelles possibilités dans l'analyse des sphéroïdes cellulaires",
      boxTitle: 'Créez un compte gratuit',
      boxText:
        'Accédez à toutes les fonctionnalités de la plateforme et commencez à analyser vos images microscopiques',
      button: 'Créer un compte',
    },
  },
  tools: {
    zoomIn: 'Zoom avant',
    zoomOut: 'Zoom arrière',
    resetView: 'Réinitialiser la vue',
    createPolygon: 'Créer un nouveau polygone',
    exitPolygonCreation: 'Quitter le mode de création de polygone',
    splitPolygon: 'Diviser le polygone en deux',
    exitSlicingMode: 'Quitter le mode de découpe',
    addPoints: 'Ajouter des points au polygone',
    exitPointAddingMode: "Quitter le mode d'ajout de points",
    undo: 'Annuler',
    redo: 'Rétablir',
    save: 'Enregistrer',
    resegment: 'Resegmenter',
    title: 'Outils',
  },
  shortcuts: {
    button: 'Raccourcis',
    editMode: 'Passer en mode édition',
    sliceMode: 'Passer en mode découpe',
    addPointMode: 'Passer en mode ajout de points',
    holdShift: 'Maintenez Maj pour ajouter automatiquement des points (en mode édition)',
    undo: 'Annuler',
    redo: 'Rétablir',
    deletePolygon: 'Supprimer le polygone sélectionné',
    cancel: "Annuler l'opération en cours",
    zoomIn: 'Zoom avant',
    zoomOut: 'Zoom arrière',
    resetView: 'Réinitialiser la vue',
    title: 'Raccourcis clavier',
    viewMode: 'Mode visualisation',
    editVerticesMode: 'Mode édition des sommets',
    addPointsMode: 'Mode ajout de points',
    createPolygonMode: 'Mode création de polygone',
    save: 'Enregistrer',
    description:
      "Ces raccourcis fonctionnent dans l'éditeur de segmentation pour un travail plus rapide et plus pratique.",
  },
  imageProcessor: {
    segmentationStarted: 'Processus de segmentation démarré...',
    startSegmentationTooltip: 'Démarrer la segmentation',
    processingTooltip: 'Traitement en cours...',
    savingTooltip: 'Enregistrement...',
    completedTooltip: 'Segmentation terminée',
    retryTooltip: 'Réessayer la segmentation',
  },
  uploader: {
    dragDrop: 'Glissez et déposez des images ici ou cliquez pour sélectionner des fichiers',
    dropFiles: 'Déposez les fichiers ici...',
    segmentAfterUploadLabel: 'Segmenter les images immédiatement après le téléchargement',
    filesToUpload: 'Fichiers à télécharger',
    uploadBtn: 'Télécharger',
    uploadError: "Une erreur s'est produite lors du téléchargement. Veuillez réessayer.",
    clickToUpload: 'Cliquez pour parcourir les fichiers',
    selectProjectLabel: 'Sélectionner un projet',
    selectProjectPlaceholder: 'Sélectionnez un projet...',
    noProjectsFound: "Aucun projet trouvé. Créez-en un d'abord.",
    imageOnly: '(Fichiers image uniquement)',
  },
  export: {
    exportCompleted: 'Exportation terminée',
    exportFailed: "Échec de l'exportation",
    title: 'Exporter les données de segmentation',
    spheroidMetrics: 'Métriques des sphéroïdes',
    visualization: 'Visualisation',
    cocoFormat: 'Format COCO',
    close: 'Fermer',
    metricsExported: 'Métriques exportées avec succès',
  },
  metrics: {
    area: 'Surface',
    perimeter: 'Périmètre',
    circularity: 'Circularité',
    sphericity: 'Sphéricité',
    solidity: 'Solidité',
    compactness: 'Compacité',
    convexity: 'Convexité',
    visualization: 'Visualisation des métriques',
    visualizationHelp: 'Représentation visuelle des métriques pour tous les sphéroïdes de cette image',
    barChart: 'Graphique à barres',
    pieChart: 'Graphique circulaire',
    comparisonChart: 'Graphique de comparaison',
    keyMetricsComparison: 'Comparaison des métriques clés',
    areaDistribution: 'Distribution des surfaces',
    shapeMetricsComparison: 'Comparaison des métriques de forme',
    noPolygonsFound: "Aucun polygone trouvé pour l'analyse",
  },
  imageStatus: {
    completed: 'Traité',
    processing: 'En cours de traitement',
    pending: 'En attente',
    failed: 'Échec',
    noImage: "Pas d'image",
    untitledImage: 'Image sans titre',
  },
  segmentation: {
    resolution: 'Résolution',
    selectPolygonForSlice: 'Sélectionnez un polygone à découper',
    selectPolygonForAddPoints: 'Sélectionnez un polygone pour ajouter des points',
    selectPolygonForEdit: 'Sélectionnez un polygone à éditer',
    status: {
      processing: 'Traitement en cours',
      queued: "En file d'attente",
      completed: 'Terminé',
      failed: 'Échoué',
    },
    queue: {
      title: "File d'attente de segmentation",
      summary: '{{total}} tâches au total ({{running}} en traitement, {{queued}} en attente)',
      noRunningTasks: 'Aucune tâche en cours',
      noQueuedTasks: 'Aucune tâche en attente',
      task: 'Tâche',
      statusRunning: 'Segmentation: {{count}} en cours{{queued}}',
      statusQueued: ', {{count}} en attente',
      statusOnlyQueued: 'Segmentation: {{count}} en attente',
      statusOnlyQueued_one: 'Segmentation: 1 en attente',
      statusOnlyQueued_other: 'Segmentation: {{count}} en attente',
      processing: 'Traitement',
      queued: 'En attente',
      statusProcessing: 'Segmentation: {{count}} en traitement',
      statusReady: 'Segmentation: Prêt',
      tasksTotal: '{{total}} tâches au total ({{running}} en traitement, {{queued}} en attente)'
    },
    notifications: {
      completed: 'Segmentation terminée avec succès',
      failed: 'Échec de la segmentation: {{error}}',
      queued: "La segmentation a été mise en file d'attente",
      started: 'La segmentation a commencé',
    },
    autoSave: {
      enabled: 'Sauvegarde automatique: Activée',
      disabled: 'Sauvegarde automatique: Désactivée',
      idle: 'Sauvegarde automatique: Inactive',
      pending: 'En attente...',
      saving: 'Enregistrement...',
      success: 'Enregistré',
      error: 'Erreur',
    },
    loading: 'Chargement de la segmentation...',
    polygon: 'Polygone',
    unsavedChanges: 'Modifications non enregistrées',
    modes: {
      editMode: 'Mode édition',
      slicingMode: 'Mode découpe',
      pointAddingMode: 'Mode ajout de points',
      view: 'Visualisation',
      editVertices: 'Éditer les sommets',
      addPoints: 'Ajouter des points',
      slice: 'Découper',
      createPolygon: 'Créer un polygone',
      deletePolygon: 'Supprimer un polygone',
    },
    totalPolygons: 'Total des polygones',
    totalVertices: 'Total des sommets',
    completedSegmentation: 'Terminé',
    mode: 'Mode',
    saveSuccess: 'Segmentation enregistrée avec succès',
    resegmentSuccess: 'Resegmentation démarrée avec succès',
    resegmentComplete: 'Resegmentation terminée avec succès',
    resegmentError: "Échec de la resegmentation de l'image",
    resegmentButton: 'Resegmenter',
    resegmentButtonTooltip: 'Relancer la segmentation sur cette image',
    polygonDeleted: 'Polygone supprimé avec succès',
    vertices: 'Sommets',
    zoom: 'Zoom',
    position: 'Position',
    selected: 'Sélectionné',
    none: 'Aucun',
    polygons: 'Polygones',
    noData: 'Aucune donnée de segmentation disponible',
    noPolygons: 'Aucun polygone trouvé',
    regions: 'Segmentations',
    helpTips: {
      title: 'Astuces:',
      edit: {
        createPoint: 'Cliquez pour créer un nouveau point',
        shiftPoints: 'Maintenez Maj pour créer automatiquement une séquence de points',
        closePolygon: 'Fermez le polygone en cliquant sur le premier point',
      },
      slice: {
        start: 'Cliquez pour commencer la découpe',
        finish: 'Cliquez à nouveau pour terminer la découpe',
        cancel: 'Appuyez sur Échap pour annuler la découpe',
      },
      addPoint: {
        hover: 'Survolez une ligne de polygone',
        click: 'Cliquez pour ajouter un point au polygone sélectionné',
        exit: "Appuyez sur Échap pour quitter le mode d'ajout de points",
      },
    },
    title: 'Éditeur de segmentation',
    clickToAddPoint: 'Cliquez pour ajouter un point',
    clickToCompletePolygon: 'Cliquez sur le premier point pour compléter le polygone',
    clickToAddFirstSlicePoint: 'Cliquez pour ajouter le premier point de découpe',
    clickToAddSecondSlicePoint: 'Cliquez pour ajouter le second point de découpe',
    polygonCreationMode: 'Mode création de polygone',
    polygonEditMode: 'Mode édition de polygone',
    polygonSliceMode: 'Mode découpe de polygone',
    polygonAddPointsMode: 'Mode ajout de points',
    viewMode: 'Mode visualisation',
  },
  termsPage: {
    title: 'Terms of Service',
    acceptance: {
      title: '1. Acceptance of Terms',
      paragraph1:
        'By accessing or using SpheroSeg, you agree to be bound by these Terms of Service and all applicable laws and regulations. If you do not agree with any of these terms, you are prohibited from using this service.',
    },
    useLicense: {
      title: '2. Use License',
      paragraph1:
        'Permission is granted to temporarily use SpheroSeg for personal, non-commercial, or academic research purposes only. This is the grant of a license, not a transfer of title.',
    },
    dataUsage: {
      title: '3. Data Usage',
      paragraph1:
        'Any data uploaded to SpheroSeg remains your property. We do not claim ownership of your content but require certain permissions to provide the service.',
    },
    limitations: {
      title: '4. Limitations',
      paragraph1:
        'In no event shall SpheroSeg be liable for any damages arising out of the use or inability to use the platform, even if we have been notified of the possibility of such damage.',
    },
    revisions: {
      title: '5. Revisions and Errata',
      paragraph1:
        'The materials appearing on SpheroSeg could include technical, typographical, or photographic errors. We do not warrant that any of the materials are accurate, complete, or current.',
    },
    governingLaw: {
      title: '6. Governing Law',
      paragraph1:
        'These terms and conditions are governed by and construed in accordance with the laws of the country in which the service is hosted, and you irrevocably submit to the exclusive jurisdiction of the courts in that location.',
    },
  },
  statsOverview: {
    totalProjects: 'Total des projets',
    totalProjectsDesc: '{count} nouveau(x) ce mois-ci',
    totalImages: "Total d'images",
    totalImagesDesc: "{count, number}% d'augmentation",
    completedSegmentations: 'Segmentations terminées',
    completedSegmentationsDesc: '{count} de plus que la période précédente',
    segmentationsToday: "Segmentations aujourd'hui",
    segmentationsTodayDesc: '+{count} par rapport à hier',
    fetchError: 'Échec du chargement des statistiques.',
    loadError: 'Impossible de charger les statistiques.',
    title: 'Aperçu du tableau de bord',
    storageUsed: 'Espace de stockage utilisé',
    recentActivity: 'Activité récente',
    moreStats: 'Voir les statistiques détaillées',
    completion: "taux d'achèvement",
    vsLastMonth: 'vs. mois dernier',
    thisMonth: 'Ce mois',
    lastMonth: 'Mois dernier',
    projectsCreated: 'Projets créés',
    imagesUploaded: 'Images téléchargées',
    storageLimit: 'Limite de stockage',
    activityTitle: 'Activité récente',
    noActivity: 'Aucune activité récente',
    activityTypes: {
      project_created: 'Projet créé',
      image_uploaded: 'Image téléchargée',
      segmentation_completed: 'Segmentation terminée',
    },
  },
  privacyPage: {
    title: 'Privacy Policy',
    introduction: {
      title: '1. Introduction',
      paragraph1:
        'This Privacy Policy explains how SpheroSeg ("we", "us", "our") collects, uses, and shares your information when you use our platform for spheroid segmentation and analysis.',
    },
    informationWeCollect: {
      title: '2. Information We Collect',
      paragraph1:
        'We collect information you provide directly to us when you create an account, upload images, create projects, and otherwise interact with our services.',
    },
    personalInformation: {
      title: '2.1 Personal Information',
      paragraph1:
        'This includes your name, email address, institution/organization, and other information you provide when setting up your account or requesting access to our services.',
    },
    researchData: {
      title: '2.2 Research Data',
      paragraph1:
        'This includes images you upload, project details, analysis results, and other research related data you create or upload to our platform.',
    },
    usageInformation: {
      title: '2.3 Usage Information',
      paragraph1:
        'We collect information about how you use our platform, including log data, device information, and usage patterns.',
    },
    howWeUse: {
      title: '3. How We Use Your Information',
      paragraph1:
        'We use the information we collect to provide, maintain, and improve our services, to communicate with you, and to comply with our legal obligations.',
    },
    dataSecurity: {
      title: '4. Data Security',
      paragraph1:
        'We implement appropriate security measures to protect your personal information and research data from unauthorized access, alteration, disclosure, or destruction.',
    },
    dataSharing: {
      title: '5. Data Sharing',
      paragraph1:
        'We do not sell your personal information or research data. We may share your information in limited circumstances, such as with your consent, to comply with legal obligations, or with service providers who help us operate our platform.',
    },
    yourChoices: {
      title: '6. Your Choices',
      paragraph1:
        'You can access, update, or delete your account information and research data through your account settings. You can also contact us to request access to, correction of, or deletion of any personal information we have about you.',
    },
    changes: {
      title: '7. Changes to This Policy',
      paragraph1:
        'We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last Updated" date.',
    },
    contactUs: {
      title: '8. Contact Us',
      paragraph1: 'If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.',
    },
    lastUpdated: 'Last Updated: July 1, 2023',
  },
  editor: {
    backButtonTooltip: "Retour à l'aperçu du projet",
    exportButtonTooltip: 'Exporter les données de segmentation actuelles',
    saveTooltip: 'Enregistrer vos modifications',
    image: 'Image',
    previousImage: 'Image précédente',
    nextImage: 'Image suivante',
    resegmentButton: 'Resegmenter',
    resegmentButtonTooltip: 'Relancer la segmentation sur cette image',
    exportMaskButton: 'Exporter le masque',
    exportMaskButtonTooltip: 'Exporter le masque de segmentation pour cette image',
    backButton: 'Retour',
    exportButton: 'Exporter',
    saveButton: 'Enregistrer',
    loadingProject: 'Chargement du projet...',
    loadingImage: "Chargement de l'image...",
    sliceErrorInvalidPolygon: 'Impossible de découper: Polygone sélectionné invalide.',
    sliceWarningInvalidResult: 'La découpe a résulté en des polygones trop petits pour être valides.',
    sliceWarningInvalidIntersections:
      'Découpe invalide: La ligne de coupe doit intersecter le polygone à exactement deux points.',
    sliceSuccess: 'Polygone découpé avec succès.',
    noPolygonToSlice: 'Aucun polygone disponible pour la découpe.',
    savingTooltip: 'Enregistrement...',
  },
  segmentationPage: {
    noImageSelected: 'Aucune image sélectionnée pour la resegmentation.',
    resegmentationStarted: 'Démarrage de la resegmentation avec le réseau de neurones ResUNet...',
    resegmentationQueued: "La resegmentation a été mise en file d'attente.",
    resegmentationCompleted: 'Resegmentation terminée avec succès.',
    resegmentationFailed: 'Échec de la resegmentation.',
    resegmentationTimeout: "Délai de resegmentation dépassé. Veuillez vérifier l'état de la file d'attente.",
    resegmentationError: 'Échec du démarrage de la resegmentation.',
    resegmentTooltip: 'Resegmenter',
  },
  errors: {
    fetchSegmentationFailed: 'Échec du chargement des données de segmentation',
    fetchImageFailed: "Échec du chargement des données d'image",
    saveSegmentationFailed: "Échec de l'enregistrement de la segmentation",
    networkError: 'Erreur réseau survenue',
    serverError: 'Erreur serveur survenue',
    unknownError: "Une erreur inconnue s'est produite",
    imageNotFound: 'Image non trouvée',
    imageNotFoundDesc: "L'image que vous essayez d'accéder n'existe pas ou a été supprimée.",
    returnToProject: 'Retourner au projet',
    goToDashboard: 'Aller au tableau de bord',
    somethingWentWrong: "Une erreur s'est produite",
    componentError: "Une erreur s'est produite dans ce composant",
    goBack: 'Retour',
    tryAgain: 'Réessayer',
    notFound: 'Page non trouvée',
    unauthorized: 'Accès non autorisé',
    forbidden: 'Accès interdit',
    timeoutError: 'Délai de requête dépassé',
    validationError: 'Erreur de validation',
    pageNotFound: 'Page non trouvée',
    pageNotFoundMessage: "La page demandée n'a pas pu être trouvée",
    goHome: "Aller à la page d'accueil",
  },
  project: {
    loading: 'Chargement du projet...',
    notFound: 'Projet non trouvé',
    error: 'Erreur lors du chargement du projet',
    empty: 'Ce projet est vide',
    noImages: 'Aucune image trouvée dans ce projet',
    addImages: 'Ajoutez des images pour commencer',
  },
  navigation: {
    home: 'Accueil',
    projects: 'Projets',
    settings: 'Paramètres',
    profile: 'Profil',
    dashboard: 'Tableau de bord',
    back: 'Retour',
  },
  accessibility: {
    skipToContent: 'Passer au contenu principal',
  },
  projectsPage: {
    title: 'Projets',
    description: 'Gérer vos projets de recherche',
    createNew: 'Créer un nouveau projet',
    createProject: 'Créer un projet',
    createProjectDesc: 'Démarrer un nouveau projet de recherche',
    projectName: 'Nom du projet',
    projectDescription: 'Description du projet',
    projectNamePlaceholder: 'Entrez le nom du projet',
    projectDescriptionPlaceholder: 'Entrez la description du projet',
    projectCreated: 'Projet créé avec succès',
    projectCreationFailed: 'Échec de la création du projet',
    projectDeleted: 'Projet supprimé avec succès',
    projectDeletionFailed: 'Échec de la suppression du projet',
    confirmDelete: 'Êtes-vous sûr de vouloir supprimer ce projet ?',
    confirmDeleteDescription:
      'Cette action ne peut pas être annulée. Toutes les données associées à ce projet seront définitivement supprimées.',
    deleteProject: 'Supprimer le projet',
    editProject: 'Modifier le projet',
    viewProject: 'Voir le projet',
    projectUpdated: 'Projet mis à jour avec succès',
    projectUpdateFailed: 'Échec de la mise à jour du projet',
    noProjects: 'Aucun projet trouvé',
    createFirstProject: 'Créez votre premier projet pour commencer',
    searchProjects: 'Rechercher des projets...',
    filterProjects: 'Filtrer les projets',
    sortProjects: 'Trier les projets',
    projectNameRequired: 'Le nom du projet est requis',
    loginRequired: 'Vous devez être connecté pour créer un projet',
    createdAt: 'Créé le',
    updatedAt: 'Dernière mise à jour',
    imageCount: 'Images',
    status: 'Statut',
    actions: 'Actions',
    loading: 'Chargement des projets...',
    error: 'Erreur lors du chargement des projets',
    retry: 'Réessayer',
    duplicating: 'Duplication du projet...',
    duplicate: 'Dupliquer',
    duplicateSuccess: 'Projet dupliqué avec succès',
    duplicateFailed: 'Échec de la duplication du projet',
    duplicateTitle: 'Dupliquer le projet',
    duplicateProject: 'Dupliquer le projet',
    duplicateProjectDescription:
      'Créer une copie de ce projet incluant toutes les images. Vous pouvez personnaliser les options ci-dessous.',
    duplicateCancelled: 'Duplication du projet annulée',
    duplicatingProject: 'Duplication du projet',
    duplicatingProjectDescription: 'Votre projet est en cours de duplication. Cela peut prendre quelques instants.',
    duplicateProgress: 'Progression de la duplication',
    duplicationComplete: 'Duplication du projet terminée',
    duplicationTaskFetchError: 'Erreur lors de la récupération des données de tâche',
    duplicationCancelError: "Erreur lors de l'annulation de la duplication",
    duplicateProgressDescription:
      'Votre projet est en cours de duplication. Ce processus peut prendre du temps pour les projets volumineux.',
    duplicationPending: 'En attente',
    duplicationProcessing: 'En cours',
    duplicationCompleted: 'Terminé',
    duplicationFailed: 'Échoué',
    duplicationCancelled: 'Annulé',
    duplicationCancellationFailed: "Échec de l'annulation de la duplication",
    duplicationSuccessMessage: 'Projet dupliqué avec succès ! Vous pouvez maintenant accéder au nouveau projet.',
    copySegmentations: 'Copier les résultats de segmentation',
    resetImageStatus: 'Réinitialiser le statut de traitement des images',
    newProjectTitle: 'Nouveau titre de projet',
    itemsProcessed: 'éléments traités',
    items: 'éléments',
    unknownProject: 'Projet inconnu',
    activeTasks: 'Actif',
    allTasks: 'Tous',
    noActiveDuplications: 'Aucune duplication active',
    noDuplications: 'Aucune tâche de duplication trouvée',
    deleteProjectDescription: 'Cette action supprimera définitivement le projet et toutes les données associées.',
    deleteWarning:
      'Cette action ne peut pas être annulée. Toutes les données associées à ce projet seront définitivement supprimées.',
    untitledProject: 'Projet sans titre',
    typeToConfirm: 'Tapez "delete" pour confirmer',
    deleteConfirm: 'Êtes-vous sûr de vouloir supprimer ce projet ?',
    exportProject: 'Exporter le projet',
    archived: 'Archivé',
    completed: 'Terminé',
    draft: 'Brouillon',
    active: 'Actif',
    createDate: 'Créé le',
    lastModified: 'Dernière modification',
    projectDescPlaceholder: 'Entrez la description du projet',
    creatingProject: 'Création du projet...',
  },
};
