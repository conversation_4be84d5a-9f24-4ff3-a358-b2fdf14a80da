export default {
  project: {
    detail: {
      noImagesSelected: 'No hay imágenes seleccionadas',
      triggeringResegmentation: 'Iniciando re-segmentación para {{count}} imágenes...',
      deleteConfirmation: '¿Está seguro de que desea eliminar {{count}} imágenes? Esta acción no se puede deshacer.',
      deletingImages: 'Eliminando {{count}} imágenes...',
      deleteSuccess: 'Se eliminaron {{count}} imágenes con éxito',
      deleteFailed: 'No se pudieron eliminar {{count}} imágenes',
      preparingExport: 'Preparando la exportación de {{count}} imágenes...'
    },
    segmentation: {
      processingInBatches: 'Iniciando segmentación para {{count}} imágenes en {{batches}} lotes...',
      batchQueued: 'Lote {{current}}/{{total}} puesto en cola con éxito',
      batchQueuedFallback: 'Lote {{current}}/{{total}} puesto en cola con éxito (endpoint alternativo)',
      batchError: 'Error al procesar el lote {{current}}/{{total}}',
      partialSuccess: 'Segmentación: {{success}} imágenes puestas en cola con éxito, {{failed}} fallaron',
      allSuccess: 'Segmentación: Todas las {{count}} imágenes puestas en cola con éxito',
      allFailed: 'Segmentación: Todas las {{count}} imágenes fallaron',
      startedImages: 'Segmentación iniciada para {{count}} imágenes',
      queuedLocallyWarning: 'Segmentación puesta en cola localmente para {{count}} imágenes. Falló la conexión con el servidor.'
    },
  },
  common: {
    appName: 'Segmentación de Esferoides',
    appNameShort: 'SpheroSeg',
    loading: 'Cargando...',
    save: 'Guardar',
    cancel: 'Cancelar',
    delete: 'Eliminar',
    edit: 'Editar',
    create: 'Crear',
    search: 'Buscar',
    error: 'Error',
    success: 'Éxito',
    back: 'Volver',
    signIn: 'Iniciar sesión',
    signUp: 'Registrarse',
    signOut: 'Cerrar sesión',
    settings: 'Configuración',
    profile: 'Perfil',
    dashboard: 'Panel de control',
    project: 'Proyecto',
    projects: 'Proyectos',
    newProject: 'Nuevo proyecto',
    upload: 'Subir',
    uploadImages: 'Subir imágenes',
    recentAnalyses: 'Análisis recientes',
    noProjects: 'No se encontraron proyectos',
    noImages: 'No se encontraron imágenes',
    createYourFirst: 'Crea tu primer proyecto para comenzar',
    tryAgain: 'Intentar de nuevo',
    email: 'Correo electrónico',
    password: 'Contraseña',
    name: 'Nombre',
    description: 'Descripción',
    date: 'Fecha',
    status: 'Estado',
    images: 'Imágenes',
    image: 'Imagen',
    projectName: 'Nombre del proyecto',
    projectDescription: 'Descripción del proyecto',
    theme: 'Tema',
    language: 'Idioma',
    light: 'Claro',
    dark: 'Oscuro',
    system: 'Sistema',
    welcome: 'Bienvenido a la plataforma de segmentación de esferoides',
    account: 'Cuenta',
    notifications: 'Notificaciones',
    passwordConfirm: 'Confirmar contraseña',
    manageAccount: 'Administrar tu cuenta',
    changePassword: 'Cambiar contraseña',
    deleteAccount: 'Eliminar cuenta',
    requestAccess: 'Solicitar acceso',
    termsOfService: 'Términos de servicio',
    privacyPolicy: 'Política de privacidad',
    accessRequest: 'Solicitud de acceso',
    createAccount: 'Crear cuenta',
    signInToAccount: 'Iniciar sesión en tu cuenta',
    sort: 'Ordenar',
    backToHome: 'Back to Home',
    termsOfServiceLink: 'Terms of Service',
    privacyPolicyLink: 'Privacy Policy',
    optional: 'Opcional',
    saveChanges: 'Guardar cambios',
    saving: 'Guardando',
    notSpecified: 'No especificado',
    no: 'No',
    profileTitle: 'Perfil',
    profileDescription: 'Actualiza tu información de perfil visible para otros usuarios',
    profileUsername: 'Nombre de usuario',
    profileUsernamePlaceholder: 'Ingresa tu nombre de usuario',
    profileFullName: 'Nombre completo',
    profileFullNamePlaceholder: 'Ingresa tu nombre completo',
    profileTitlePlaceholder: 'ej. Investigador científico, Profesor',
    profileOrganization: 'Organización',
    profileOrganizationPlaceholder: 'Ingresa tu organización o institución',
    profileBio: 'Biografía',
    profileBioPlaceholder: 'Escribe una breve biografía sobre ti',
    profileBioDescription: 'Breve descripción sobre tus intereses de investigación y experiencia',
    profileLocation: 'Ubicación',
    profileLocationPlaceholder: 'ej. Madrid, España',
    profileSaveButton: 'Guardar perfil',
    reset: 'Restablecer',
    clear: 'Limpiar',
    download: 'Descargar',
    removeAll: 'Eliminar todo',
    confirmPassword: 'Confirmar contraseña',
    firstName: 'Nombre',
    lastName: 'Apellido',
    username: 'Nombre de usuario',
    enable: 'Habilitar',
    disable: 'Deshabilitar',
    and: 'y',
    lastChange: 'Último cambio',
    emailPlaceholder: 'Ingresa tu correo electrónico',
    passwordPlaceholder: 'Ingresa tu contraseña',
    export: 'Exportar',
    selectImages: 'Seleccionar imágenes',
    noImagesDescription: 'Sube imágenes para comenzar con tu proyecto',
    yes: 'Sí',
    editor: {
      error: 'Error',
      success: 'Éxito',
      edit: 'Editar',
      create: 'Crear',
    },
  },
  requestAccess: {
    title: 'Solicitar acceso a la plataforma de segmentación de esferoides',
    description:
      'Completa el siguiente formulario para solicitar acceso a nuestra plataforma. Revisaremos tu solicitud y nos pondremos en contacto contigo pronto.',
    emailLabel: 'Tu dirección de correo electrónico',
    nameLabel: 'Tu nombre',
    institutionLabel: 'Institución/Empresa',
    reasonLabel: 'Motivo del acceso',
    submitRequest: 'Enviar solicitud',
    requestReceived: 'Solicitud recibida',
    thankYou: 'Gracias por tu interés',
    weWillContact: 'Revisaremos tu solicitud y nos pondremos en contacto contigo pronto',
    submitSuccess: '¡Solicitud enviada con éxito!',
    and: 'y',
  },
  documentation: {
    tag: 'Guía del usuario',
    title: 'Documentación de SpheroSeg',
    subtitle: 'Aprende a usar la plataforma de segmentación de esferoides de manera efectiva.',
    sidebar: {
      title: 'Secciones',
      introduction: 'Introducción',
      gettingStarted: 'Comenzando',
      uploadingImages: 'Subir imágenes',
      segmentationProcess: 'Proceso de segmentación',
      apiReference: 'Referencia de la API',
    },
    introduction: {
      title: 'Introducción',
      imageAlt: 'Ilustración del flujo de trabajo de análisis de esferoides',
      whatIs: {
        title: '¿Qué es SpheroSeg?',
        paragraph1: 'SpheroSeg es una plataforma de vanguardia diseñada para...',
        paragraph2: 'Utiliza algoritmos avanzados de IA para...',
        paragraph3: 'Esta documentación te guiará a través de...',
      },
    },
    gettingStarted: {
      title: 'Comenzando',
      accountCreation: {
        title: 'Creación de cuenta',
        paragraph1: 'Para comenzar a usar SpheroSeg, necesitas una cuenta...',
        step1Prefix: 'Navega a la',
        step1Link: 'página de solicitud de acceso',
        step2: 'Completa los detalles requeridos.',
        step3: 'Envía el formulario.',
        step4: 'Espera la aprobación del administrador.',
      },
      creatingProject: {
        title: 'Creando tu primer proyecto',
        paragraph1: 'Los proyectos te ayudan a organizar tus imágenes y análisis...',
        step1: 'Inicia sesión en tu cuenta.',
        step2: 'Ve al panel de control.',
        step3: 'Haz clic en "Nuevo proyecto".',
        step4: 'Ingresa un nombre y descripción para tu proyecto.',
      },
    },
    uploadingImages: {
      title: 'Subir imágenes',
      paragraph1: 'Una vez que tengas un proyecto, puedes subir tus imágenes microscópicas...',
      methods: {
        title: 'Métodos de carga',
        paragraph1: 'Puedes subir imágenes usando el siguiente método:',
        step1: 'Navega a tu proyecto.',
        step2: 'Haz clic en el botón "Subir imágenes".',
        step3: 'Arrastra y suelta tus archivos o haz clic para seleccionarlos.',
      },
      note: {
        prefix: 'Nota:',
        text: 'Asegúrate de que tus imágenes estén en formatos aceptados (JPEG, PNG, TIFF, BMP) y dentro del límite de tamaño.',
      },
    },
    segmentationProcess: {
      title: 'Proceso de segmentación',
      paragraph1: 'SpheroSeg ofrece herramientas de segmentación automáticas y manuales...',
      automatic: {
        title: 'Segmentación automática',
        paragraph1: 'Nuestro modelo de IA puede segmentar automáticamente esferoides...',
        step1: 'Selecciona las imágenes que deseas analizar.',
        step2: 'Elige el modelo de segmentación deseado (si es aplicable).',
        step3: 'Inicia el proceso de análisis.',
        step4: 'Los resultados aparecerán una vez que se complete el procesamiento.',
      },
      manual: {
        title: 'Herramientas de edición manual',
        paragraph1: 'Puedes refinar segmentaciones automáticas o crear nuevas manualmente...',
        step1: 'Abre una imagen en el editor.',
        step2: 'Selecciona la herramienta apropiada (por ejemplo, Añadir polígono, Editar vértices).',
        step3: 'Haz tus ajustes en el lienzo de la imagen.',
        step4: 'Guarda tus cambios regularmente.',
      },
    },
    apiReference: {
      title: 'Referencia de la API',
      paragraph1: 'Para usuarios avanzados, SpheroSeg proporciona una API REST...',
      endpoint1Desc: 'Obtén una lista de tus proyectos.',
      endpoint2Desc: 'Obtén imágenes dentro de un proyecto específico.',
      endpoint3Desc: 'Inicia la segmentación para una imagen específica.',
      contactPrefix: '<EMAIL>',
    },
    backToHome: 'Volver al inicio',
    backToTop: 'Volver arriba',
  },
  dashboard: {
    manageProjects: 'Administra tus proyectos de investigación y análisis',
    statsOverview: 'Resumen de estadísticas',
    totalProjects: 'Total de proyectos',
    activeProjects: 'Proyectos activos',
    totalImages: 'Total de imágenes',
    totalAnalyses: 'Total de análisis',
    lastUpdated: 'Última actualización',
    noProjectsDescription: 'Aún no has creado ningún proyecto. Crea tu primer proyecto para comenzar.',
    noImagesDescription: 'Sube algunas imágenes para comenzar',
    searchProjectsPlaceholder: 'Buscar proyectos...',
    searchImagesPlaceholder: 'Buscar imágenes por nombre...',
    sortBy: 'Ordenar por',
    name: 'Nombre',
    lastChange: 'Último cambio',
    status: 'Estado',
    completed: 'Completado',
    processing: 'Procesando',
    pending: 'Pendiente',
    failed: 'Fallido',
    selectImagesButton: 'Select Images',
    viewMode: {
      grid: 'Vista de cuadrícula',
      list: 'Vista de lista',
    },
    sort: {
      name: 'Nombre',
      updatedAt: 'Última actualización',
      segmentationStatus: 'Estado',
    },
    search: 'Buscar proyectos...',
    noProjects: 'No se encontraron proyectos',
    createFirst: 'Crea tu primer proyecto para comenzar',
    createNew: 'Crear nuevo proyecto',
  },
  projects: {
    createProject: 'Crear nuevo proyecto',
    createProjectDesc: 'Añade un nuevo proyecto para organizar tus imágenes y análisis de esferoides.',
    projectNamePlaceholder: 'ej. Esferoides de células HeLa',
    projectDescPlaceholder: 'ej. Análisis de esferoides tumorales para estudios de resistencia a fármacos',
    creatingProject: 'Creando...',
    duplicateProject: 'Duplicar',
    shareProject: 'Compartir',
    deleteProject: 'Eliminar',
    openProject: 'Abrir proyecto',
    confirmDelete: '¿Estás seguro de que quieres eliminar este proyecto?',
    projectCreated: 'Proyecto creado con éxito',
    projectDeleted: 'Proyecto eliminado con éxito',
    viewProject: 'Ver proyecto',
    projectImages: 'Imágenes del proyecto',
    projectSelection: 'Selección de proyecto',
    selectProject: 'Seleccionar un proyecto',
    projectNameRequired: 'Por favor ingresa un nombre de proyecto',
    loginRequired: 'Debes iniciar sesión para crear un proyecto',
    createSuccess: 'Proyecto creado con éxito',
    createError: 'Error al crear el proyecto',
    invalidData: 'Datos de proyecto no válidos',
    title: 'Proyectos',
    description: 'Administra tus proyectos de investigación',
    createNew: 'Crear nuevo proyecto',
    projectName: 'Nombre del proyecto',
    projectDescription: 'Descripción del proyecto',
    projectDescriptionPlaceholder: 'Ingresa la descripción del proyecto',
    projectCreationFailed: 'Error al crear el proyecto',
    projectDeletionFailed: 'Error al eliminar el proyecto',
    confirmDeleteDescription:
      'Esta acción no se puede deshacer. Todos los datos asociados con este proyecto se eliminarán permanentemente.',
    editProject: 'Editar proyecto',
    projectUpdated: 'Proyecto actualizado con éxito',
    projectUpdateFailed: 'Error al actualizar el proyecto',
    noProjects: 'No se encontraron proyectos',
    createFirstProject: 'Crea tu primer proyecto para comenzar',
    searchProjects: 'Buscar proyectos...',
    filterProjects: 'Filtrar proyectos',
    sortProjects: 'Ordenar proyectos',
    createdAt: 'Creado',
    updatedAt: 'Última actualización',
    imageCount: 'Imágenes',
    status: 'Estado',
    actions: 'Acciones',
    loading: 'Cargando proyectos...',
    error: 'Error al cargar proyectos',
    retry: 'Reintentar',
  },
  projectActions: {
    duplicateTooltip: 'Duplicar proyecto',
    deleteTooltip: 'Eliminar proyecto',
    deleteConfirmTitle: '¿Estás seguro?',
    deleteConfirmDesc: '¿Realmente quieres eliminar el proyecto "{{projectName}}"? Esta acción no se puede deshacer.',
    deleteSuccess: 'Proyecto "{{projectName}}" eliminado correctamente.',
    deleteError: 'Error al eliminar el proyecto.',
    duplicateSuccess: 'Proyecto "{{projectName}}" duplicado correctamente.',
    duplicateError: 'Error al duplicar el proyecto.',
    makePrivateTooltip: 'Marcar como privado',
    makePublicTooltip: 'Marcar como público',
    shareTooltip: 'Compartir proyecto',
    downloadTooltip: 'Descargar proyecto',
    notFound: 'El proyecto "{{projectName}}" no fue encontrado. Es posible que ya haya sido eliminado.',
  },
  projectToolbar: {
    selectImages: 'Select Images',
    cancelSelection: 'Cancel Selection',
    export: 'Export',
    uploadImages: 'Subir imágenes',
  },
  images: {
    uploadImages: 'Subir imágenes',
    dragDrop: 'Arrastra y suelta imágenes aquí',
    clickToSelect: 'o haz clic para seleccionar archivos',
    acceptedFormats: 'Formatos aceptados: JPEG, PNG, TIFF, BMP (máx. 10MB)',
    uploadProgress: 'Progreso de la carga',
    uploadingTo: 'Subiendo a',
    currentProject: 'Proyecto actual',
    autoSegment: 'Segmentar automáticamente las imágenes después de la carga',
    uploadCompleted: 'Carga completada',
    uploadFailed: 'Carga fallida',
    imagesUploaded: 'Imágenes subidas con éxito',
    imagesFailed: 'Error al subir imágenes',
    viewAnalyses: 'Ver análisis',
    noAnalysesYet: 'Aún no hay análisis',
    runAnalysis: 'Ejecutar análisis',
    viewResults: 'Ver resultados',
    dropImagesHere: 'Suelta las imágenes aquí...',
    selectProjectFirst: 'Por favor selecciona un proyecto primero',
    projectRequired: 'Debes seleccionar un proyecto antes de poder subir imágenes',
    imageOnly: '(Solo archivos de imagen)',
    dropFiles: 'Suelta los archivos aquí...',
    filesToUpload: 'Archivos para subir ({{count}})',
    uploadBtn: 'Subir {{count}} imágen(es)',
    uploadError: 'Ocurrió un error durante la carga. Por favor, inténtalo de nuevo.',
    noProjectsToUpload: 'No hay proyectos disponibles. Cree un proyecto primero.',
    notFound: 'El proyecto "{{projectName}}" no fue encontrado. Es posible que ya haya sido eliminado.',
  },
  settings: {
    manageSettings: 'Administra las preferencias de tu cuenta',
    appearance: 'Apariencia',
    themeSettings: 'Configuración del tema',
    systemDefault: 'Predeterminado del sistema',
    languageSettings: 'Configuración de idioma',
    selectLanguage: 'Seleccionar idioma',
    changeLanguage: 'Cambiar idioma',
    useBrowserLanguage: 'Usar idioma del navegador',
    accountSettings: 'Configuración de la cuenta',
    notificationSettings: 'Configuración de notificaciones',
    emailNotifications: 'Notificaciones por correo electrónico',
    pushNotifications: 'Notificaciones push',
    profileSettings: 'Configuración del perfil',
    profileUpdated: 'Perfil actualizado con éxito',
    profileUpdateFailed: 'Error al actualizar el perfil',
    saveChanges: 'Guardar cambios',
    savingChanges: 'Guardando cambios...',
    notifications: {
      projectUpdates: 'Actualizaciones de proyectos',
      analysisCompleted: 'Análisis completado',
      newFeatures: 'Nuevas características',
      marketingEmails: 'Correos de marketing',
      billing: 'Notificaciones de facturación',
    },
    pageTitle: 'Configuración',
    profile: 'Perfil',
    account: 'Cuenta',
    profileTitle: 'Información de perfil',
    profileDescription: 'Actualiza tu información de perfil visible para otros usuarios',
    username: 'Nombre de usuario',
    usernamePlaceholder: 'Ingresa tu nombre de usuario',
    fullNamePlaceholder: 'Ingresa tu nombre completo',
    title: 'Título',
    titlePlaceholder: 'ej. Investigador científico, Profesor',
    organizationPlaceholder: 'Ingresa tu organización o institución',
    bio: 'Biografía',
    bioPlaceholder: 'Escribe una breve biografía sobre ti',
    bioDescription: 'Breve descripción sobre tus intereses de investigación y experiencia',
    location: 'Ubicación',
    locationPlaceholder: 'ej. Madrid, España',
    fetchError: 'Error al obtener datos del perfil',
    updateSuccess: 'Perfil actualizado con éxito',
    updateError: 'Error al actualizar el perfil',
    noChanges: 'No hay cambios para guardar',
    profileLoadError: 'Error al cargar datos del perfil',
    languageUpdated: 'Idioma actualizado con éxito',
    themeUpdated: 'Tema actualizado con éxito',
    appearanceDescription: 'Personaliza la apariencia de la aplicación',
    languageDescription: 'Selecciona tu idioma preferido',
    notificationsTab: 'Notificaciones',
    personal: 'Información personal',
    fullName: 'Nombre completo',
    organization: 'Organización',
    department: 'Departamento',
    publicProfile: 'Perfil público',
    makeProfileVisible: 'Hacer mi perfil visible para otros investigadores',
    dangerZone: 'Zona de peligro',
    deleteAccountWarning:
      'Una vez que elimines tu cuenta, no hay vuelta atrás. Todos tus datos serán eliminados permanentemente.',
    currentPassword: 'Contraseña actual',
    newPassword: 'Nueva contraseña',
    confirmNewPassword: 'Confirmar nueva contraseña',
    securitySettings: 'Configuración de seguridad',
    preferenceSettings: 'Configuración de preferencias',
    selectTheme: 'Seleccionar tema',
    updateProfile: 'Actualizar perfil',
    changePassword: 'Cambiar contraseña',
    deleteAccount: 'Eliminar cuenta',
    savedChanges: 'Cambios guardados con éxito',
    privacySettings: 'Configuración de privacidad',
    exportData: 'Exportar datos',
    importData: 'Importar datos',
    uploadAvatar: 'Subir foto de perfil',
    removeAvatar: 'Eliminar foto de perfil',
    twoFactorAuth: 'Autenticación de dos factores',
    weeklyDigest: 'Resumen semanal',
    monthlyReport: 'Informe mensual',
    displaySettings: 'Configuración de pantalla',
    accessibilitySettings: 'Configuración de accesibilidad',
    advancedSettings: 'Configuración avanzada',
    language: 'Idioma',
    theme: 'Tema',
    light: 'Claro',
    dark: 'Oscuro',
    system: 'Sistema',
    themeDescription: 'Elige tu tema preferido',
  },
  auth: {
    signIn: 'Iniciar sesión',
    signUp: 'Registrarse',
    signOut: 'Cerrar sesión',
    forgotPassword: '¿Olvidaste tu contraseña?',
    forgotPasswordTitle: 'Restablecer tu contraseña',
    checkYourEmail: 'Revisa tu correo electrónico para el enlace de restablecimiento',
    enterEmailForReset:
      'Ingresa tu dirección de correo electrónico y te enviaremos un enlace para restablecer tu contraseña',
    passwordResetLinkSent:
      'Si existe una cuenta con este correo electrónico, se ha enviado un enlace para restablecer la contraseña',
    passwordResetFailed: 'Error al enviar el enlace para restablecer la contraseña. Por favor, inténtalo de nuevo.',
    enterEmail: 'Por favor ingresa tu dirección de correo electrónico',
    sendingResetLink: 'Enviando enlace de restablecimiento...',
    sendResetLink: 'Enviar enlace de restablecimiento',
    backToSignIn: 'Volver a iniciar sesión',
    resetPassword: 'Restablecer contraseña',
    dontHaveAccount: '¿No tienes una cuenta?',
    alreadyHaveAccount: '¿Ya tienes una cuenta?',
    signInWith: 'Iniciar sesión con',
    signUpWith: 'Registrarse con',
    orContinueWith: 'o continuar con',
    rememberMe: 'Recordarme',
    emailRequired: 'El correo electrónico es obligatorio',
    passwordRequired: 'La contraseña es obligatoria',
    invalidEmail: 'Dirección de correo electrónico no válida',
    passwordTooShort: 'La contraseña debe tener al menos 6 caracteres',
    passwordsDontMatch: 'Las contraseñas no coinciden',
    mustAgreeTerms: 'Debes aceptar los términos y condiciones',
    signUpSuccessEmail:
      'Registro exitoso! Por favor, revisa tu correo electrónico o espera la aprobación del administrador.',
    signUpFailed: 'Error en el registro. Por favor, inténtalo de nuevo.',
    successfulSignIn: 'Inicio de sesión exitoso',
    successfulSignUp: 'Registro exitoso',
    verifyEmail: 'Por favor, revisa tu correo electrónico para confirmar tu cuenta',
    successfulSignOut: 'Sesión cerrada exitosamente',
    checkingAuthentication: 'Verificando autenticación...',
    loadingAccount: 'Cargando tu cuenta...',
    processingRequest: 'Procesando tu solicitud...',
    firstNamePlaceholder: 'ej., Max',
    lastNamePlaceholder: 'ej., Robinson',
    emailPlaceholder: '<EMAIL>',
    passwordPlaceholder: '••••••••',
    alreadyLoggedInTitle: 'Ya has iniciado sesión',
    alreadyLoggedInMessage: 'Ya has iniciado sesión en tu cuenta.',
    goToDashboardLink: 'Ir al panel de control',
    emailAddressLabel: 'Dirección de correo electrónico',
    passwordLabel: 'Contraseña',
    signingIn: 'Iniciando sesión...',
    fillAllFields: 'Por favor, completa todos los campos',
    signInToAccount: 'Iniciar sesión en tu cuenta',
    accessPlatform: 'Accede a la plataforma de segmentación de esferoides',
    requestAccess: 'Solicitar acceso',
    termsAndPrivacy: 'Al iniciar sesión, aceptas nuestros términos de servicio y política de privacidad',
    createAccount: 'Crear cuenta',
    signInWithGoogle: 'Iniciar sesión con Google',
    signInWithGithub: 'Iniciar sesión con GitHub',
    or: 'o',
    signInTitle: 'Iniciar sesión',
    signInDescription: 'Iniciar sesión en tu cuenta',
    noAccount: '¿No tienes una cuenta?',
    currentPasswordLabel: 'Contraseña actual',
    newPasswordLabel: 'Nueva contraseña',
    confirmPasswordLabel: 'Confirmar contraseña',
    invalidCredentials: 'Correo electrónico o contraseña no válidos',
    accountCreated: 'Cuenta creada con éxito',
    resetLinkSent: 'Enlace de restablecimiento de contraseña enviado a tu correo electrónico',
    resetSuccess: 'Contraseña restablecida con éxito',
    signInSuccess: 'Sesión iniciada con éxito',
    signOutSuccess: 'Sesión cerrada con éxito',
    sessionExpired: 'Tu sesión ha expirado. Por favor, inicia sesión de nuevo.',
    unauthorized: 'No estás autorizado para acceder a este recurso',
    verificationLinkSent: 'Enlace de verificación enviado a tu correo electrónico',
    verificationSuccess: 'Correo electrónico verificado con éxito',
    resendVerification: 'Reenviar correo de verificación',
    forgotPasswordLink: '¿Olvidaste tu contraseña?',
    passwordChanged: 'Contraseña cambiada con éxito',
    currentPasswordIncorrect: 'La contraseña actual es incorrecta',
    registerTitle: 'Crear cuenta',
    registerDescription: 'Regístrate para una nueva cuenta',
    registerSuccess: '¡Registro exitoso! Ahora puedes iniciar sesión.',
  },
  requestAccessForm: {
    title: 'Solicitar acceso a la plataforma de segmentación de esferoides',
    description:
      'Completa el siguiente formulario para solicitar acceso a nuestra plataforma. Revisaremos tu solicitud y nos pondremos en contacto contigo pronto.',
    emailLabel: 'Tu dirección de correo electrónico',
    nameLabel: 'Tu nombre',
    institutionLabel: 'Institución/Empresa',
    reasonLabel: 'Motivo del acceso',
    submitButton: 'Enviar solicitud',
    signInPrompt: '¿Ya tienes una cuenta?',
    signInLink: 'Iniciar sesión',
    thankYouTitle: 'Gracias por tu interés',
    weWillContact: 'Revisaremos tu solicitud y nos pondremos en contacto contigo pronto',
    agreeToTerms: 'Al enviar esta solicitud, aceptas nuestros',
    and: 'y',
  },
  profile: {
    title: 'Perfil',
    about: 'Acerca de',
    activity: 'Actividad',
    projects: 'Proyectos',
    recentProjects: 'Proyectos recientes',
    recentAnalyses: 'Análisis recientes',
    accountDetails: 'Detalles de la cuenta',
    accountType: 'Tipo de cuenta',
    joinDate: 'Fecha de registro',
    lastActive: 'Última actividad',
    projectsCreated: 'Proyectos creados',
    imagesUploaded: 'Imágenes subidas',
    segmentationsCompleted: 'Segmentaciones completadas',
    pageTitle: 'Perfil de usuario',
    editProfile: 'Editar perfil',
    joined: 'Registrado',
    statistics: 'Estadísticas',
    images: 'Imágenes',
    analyses: 'Análisis',
    storageUsed: 'Almacenamiento utilizado',
    recentActivity: 'Actividad reciente',
    noRecentActivity: 'No hay actividad reciente',
    fetchError: 'Error al obtener datos del perfil',
    aboutMe: 'Acerca de mí',
    noBio: 'No se ha proporcionado biografía',
    avatarHelp: 'Haz clic en el icono de la cámara para subir una foto de perfil',
    avatarImageOnly: 'Por favor selecciona un archivo de imagen',
    avatarTooLarge: 'La imagen debe ser menor de 5MB',
    avatarUpdated: 'Foto de perfil actualizada',
    avatarUploadError: 'Error al subir la foto de perfil',
    avatarRemoved: 'Foto de perfil eliminada',
    avatarRemoveError: 'Error al eliminar la foto de perfil',
    description: 'Actualiza tu información personal y foto de perfil',
    saveButton: 'Guardar perfil',
    username: 'Nombre de usuario',
    usernamePlaceholder: 'Ingresa tu nombre de usuario',
    fullName: 'Nombre completo',
    fullNamePlaceholder: 'Ingresa tu nombre completo',
    titlePlaceholder: 'Ingresa tu título o cargo',
    organization: 'Organización',
    organizationPlaceholder: 'Ingresa tu organización o empresa',
    bio: 'Biografía',
    bioPlaceholder: 'Cuéntanos sobre ti',
    bioDescription: 'Una breve descripción sobre ti que será visible en tu perfil',
    location: 'Ubicación',
    locationPlaceholder: 'Ingresa tu ubicación',
  },
  hero: {
    platformTag: 'Plataforma Avanzada de Segmentación de Esferoides',
    title: 'Análisis Celular Impulsado por IA para la Investigación Biomédica',
    subtitle:
      'Eleva tu análisis de imágenes celulares microscópicas con nuestra plataforma de segmentación de esferoides de vanguardia. Diseñada para investigadores que buscan precisión y eficiencia.',
    getStartedButton: 'Comenzar',
    learnMoreButton: 'Saber más',
    imageAlt1: 'Imagen microscópica de esferoide',
    imageAlt2: 'Imagen microscópica de esferoide con análisis',
    welcomeTitle: 'Bienvenido a SpheroSeg',
    welcomeSubtitle: 'Plataforma avanzada para la segmentación y análisis de esferoides celulares',
    welcomeDescription:
      'Nuestra plataforma combina algoritmos de inteligencia artificial de vanguardia con una interfaz intuitiva para la detección y análisis precisos de esferoides celulares en imágenes microscópicas.',
    featuresTitle: 'Capacidades Potentes',
    featuresSubtitle: 'Herramientas Avanzadas para la Investigación Biomédica',
    featureAiSegmentation: 'Segmentación Avanzada',
    featureAiSegmentationDesc:
      'Detección precisa de esferoides con análisis de límites para mediciones celulares exactas.',
    featureEditing: 'Análisis Impulsado por IA',
    featureEditingDesc:
      'Aproveche los algoritmos de aprendizaje profundo para la detección y clasificación celular automatizada.',
    featureAnalytics: 'Cargas sin Esfuerzo',
    featureAnalyticsDesc: 'Arrastre y suelte sus imágenes microscópicas para procesamiento y análisis instantáneos.',
    featureExport: 'Perspectivas Estadísticas',
    featureExportDesc: 'Métricas y visualizaciones completas para extraer patrones de datos significativos.',
    ctaTitle: '¿Listo para Transformar su Flujo de Trabajo de Análisis Celular?',
    ctaSubtitle:
      'Únase a los investigadores líderes que ya están utilizando nuestra plataforma para acelerar sus descubrimientos.',
    ctaButton: 'Crear cuenta',
  },
  navbar: {
    home: 'Inicio',
    features: 'Características',
    documentation: 'Documentación',
    terms: 'Términos',
    privacy: 'Privacidad',
    login: 'Iniciar sesión',
    requestAccess: 'Solicitar acceso',
  },
  footer: {
    developerName: 'Bc. Michal Průšek',
    facultyName: 'FJFI ČVUT v Praze',
    copyrightNotice: '© 2025 SpheroSeg.',
    description: 'Plataforma avanzada para segmentación y análisis de esferoides celulares',
    contactLabel: '<EMAIL>',
    developerLabel: 'Bc. Michal Průšek',
    facultyLabel: 'FJFI ČVUT en Praga',
    resourcesTitle: 'Recursos',
    documentationLink: 'Documentación',
    featuresLink: 'Características',
    tutorialsLink: 'Tutoriales',
    researchLink: 'Investigación',
    legalTitle: 'Legal',
    termsLink: 'Términos de servicio',
    privacyLink: 'Privacidad',
    contactUsLink: 'Contáctanos',
    informationTitle: 'Información',
    contactTitle: 'Contacto',
  },
  features: {
    tag: 'Características',
    title: 'Descubre las capacidades de nuestra plataforma',
    subtitle: 'Herramientas avanzadas para investigación biomédica',
    cards: {
      segmentation: {
        title: 'Segmentación avanzada',
        description: 'Detección precisa de esferoides con análisis de bordes para mediciones celulares exactas',
      },
      aiAnalysis: {
        title: 'Análisis impulsado por IA',
        description: 'Utiliza algoritmos de aprendizaje profundo para detección y clasificación celular automatizada',
      },
      uploads: {
        title: 'Carga sencilla',
        description: 'Arrastra y suelta tus imágenes microscópicas para procesamiento y análisis inmediato',
      },
      insights: {
        title: 'Estadísticas detalladas',
        description: 'Métricas y visualizaciones completas para extraer patrones significativos de datos',
      },
      collaboration: {
        title: 'Colaboración en equipo',
        description: 'Comparte proyectos y resultados con colegas para una investigación más eficiente',
      },
      pipeline: {
        title: 'Proceso automatizado',
        description: 'Simplifica tu flujo de trabajo con nuestras herramientas de procesamiento por lotes',
      },
    },
  },
  index: {
    about: {
      tag: 'Sobre la plataforma',
      title: '¿Qué es SpheroSeg?',
      imageAlt: 'Ejemplo de segmentación de esferoide',
      paragraph1:
        'SpheroSeg es una plataforma avanzada diseñada específicamente para la segmentación y análisis de esferoides celulares en imágenes microscópicas.',
      paragraph2:
        'Nuestra herramienta combina algoritmos de inteligencia artificial de vanguardia con una interfaz intuitiva para proporcionar a los investigadores detección precisa de bordes de esferoides y capacidades analíticas.',
      paragraph3:
        'La plataforma fue desarrollada por Michal Průšek de FNSPE CTU en Praga bajo la supervisión de Adam Novozámský de UTIA CAS, en colaboración con investigadores del Departamento de Bioquímica y Microbiología de UCT Praga.',
      contactPrefix: '<EMAIL>',
    },
    cta: {
      title: '¿Listo para transformar tu investigación?',
      subtitle: 'Comienza a usar SpheroSeg hoy y descubre nuevas posibilidades en el análisis de esferoides celulares',
      boxTitle: 'Crea una cuenta gratuita',
      boxText: 'Obtén acceso a todas las funciones de la plataforma y comienza a analizar tus imágenes microscópicas',
      button: 'Crear cuenta',
    },
  },
  tools: {
    zoomIn: 'Acercar',
    zoomOut: 'Alejar',
    resetView: 'Restablecer vista',
    createPolygon: 'Crear nuevo polígono',
    exitPolygonCreation: 'Salir del modo de creación de polígono',
    splitPolygon: 'Dividir polígono en dos',
    exitSlicingMode: 'Salir del modo de corte',
    addPoints: 'Añadir puntos al polígono',
    exitPointAddingMode: 'Salir del modo de añadir puntos',
    undo: 'Deshacer',
    redo: 'Rehacer',
    save: 'Guardar',
    resegment: 'Resegmentar',
    title: 'Herramientas',
  },
  shortcuts: {
    button: 'Atajos',
    editMode: 'Cambiar a modo de edición',
    sliceMode: 'Cambiar a modo de corte',
    addPointMode: 'Cambiar a modo de añadir punto',
    holdShift: 'Mantén presionada la tecla Shift para añadir puntos automáticamente (en modo de edición)',
    undo: 'Deshacer',
    redo: 'Rehacer',
    deletePolygon: 'Eliminar polígono seleccionado',
    cancel: 'Cancelar operación actual',
    zoomIn: 'Acercar',
    zoomOut: 'Alejar',
    resetView: 'Restablecer vista',
    title: 'Atajos de teclado',
    viewMode: 'Modo de vista',
    editVerticesMode: 'Modo de edición de vértices',
    addPointsMode: 'Modo de añadir puntos',
    createPolygonMode: 'Modo de creación de polígono',
    save: 'Guardar',
    description: 'Estos atajos funcionan dentro del editor de segmentación para un trabajo más rápido y cómodo.',
  },
  imageProcessor: {
    segmentationStarted: 'Proceso de segmentación iniciado...',
    startSegmentationTooltip: 'Iniciar segmentación',
    processingTooltip: 'Procesando...',
    savingTooltip: 'Guardando...',
    completedTooltip: 'Segmentación completada',
    retryTooltip: 'Reintentar segmentación',
  },
  uploader: {
    dragDrop: 'Arrastra y suelta imágenes aquí o haz clic para seleccionar archivos',
    dropFiles: 'Suelta archivos aquí...',
    segmentAfterUploadLabel: 'Segmentar imágenes inmediatamente después de la carga',
    filesToUpload: 'Archivos para cargar',
    uploadBtn: 'Cargar',
    uploadError: 'Ocurrió un error durante la carga. Por favor, inténtalo de nuevo.',
    clickToUpload: 'Haz clic para explorar archivos',
    selectProjectLabel: 'Seleccionar proyecto',
    selectProjectPlaceholder: 'Selecciona un proyecto...',
    noProjectsFound: 'No se encontraron proyectos. Crea uno primero.',
    imageOnly: '(Solo archivos de imagen)',
  },
  export: {
    exportCompleted: 'Exportación completada',
    exportFailed: 'Exportación fallida',
    title: 'Exportar datos de segmentación',
    spheroidMetrics: 'Métricas de esferoides',
    visualization: 'Visualización',
    cocoFormat: 'Formato COCO',
    close: 'Cerrar',
    metricsExported: 'Métricas exportadas con éxito',
  },
  metrics: {
    area: 'Área',
    perimeter: 'Perímetro',
    circularity: 'Circularidad',
    sphericity: 'Esfericidad',
    solidity: 'Solidez',
    compactness: 'Compacidad',
    convexity: 'Convexidad',
    visualization: 'Visualización de métricas',
    visualizationHelp: 'Representación visual de métricas para todos los esferoides en esta imagen',
    barChart: 'Gráfico de barras',
    pieChart: 'Gráfico circular',
    comparisonChart: 'Gráfico comparativo',
    keyMetricsComparison: 'Comparación de métricas clave',
    areaDistribution: 'Distribución de área',
    shapeMetricsComparison: 'Comparación de métricas de forma',
    noPolygonsFound: 'No se encontraron polígonos para análisis',
  },
  imageStatus: {
    completed: 'Procesado',
    processing: 'Procesando',
    pending: 'En espera',
    failed: 'Fallido',
    noImage: 'Sin imagen',
    untitledImage: 'Imagen sin título',
  },
  segmentation: {
    resolution: 'Resolución',
    selectPolygonForSlice: 'Selecciona un polígono para cortar',
    selectPolygonForAddPoints: 'Selecciona un polígono para añadir puntos',
    selectPolygonForEdit: 'Selecciona un polígono para editar',
    status: {
      processing: 'Procesando',
      queued: 'En cola',
      completed: 'Completado',
      failed: 'Fallido',
    },
    queue: {
      title: 'Cola de segmentación',
      summary: '{{total}} tareas en total ({{running}} procesando, {{queued}} en cola)',
      noRunningTasks: 'No hay tareas en ejecución',
      noQueuedTasks: 'No hay tareas en cola',
      task: 'Tarea',
      statusRunning: 'Segmentación: {{count}} en ejecución{{queued}}',
      statusQueued: ', {{count}} en cola',
      statusOnlyQueued: 'Segmentación: {{count}} en cola',
      statusOnlyQueued_one: 'Segmentación: 1 en cola',
      statusOnlyQueued_other: 'Segmentación: {{count}} en cola',
    },
    notifications: {
      completed: 'Segmentación completada con éxito',
      failed: 'Segmentación fallida: {{error}}',
      queued: 'La segmentación ha sido puesta en cola',
      started: 'La segmentación ha comenzado',
    },
    autoSave: {
      enabled: 'Guardado automático: Habilitado',
      disabled: 'Guardado automático: Desactivado',
      idle: 'Guardado automático: Inactivo',
      pending: 'Pendiente...',
      saving: 'Guardando...',
      success: 'Guardado',
      error: 'Error',
    },
    loading: 'Cargando segmentación...',
    polygon: 'Polígono',
    unsavedChanges: 'Cambios no guardados',
    modes: {
      editMode: 'Modo de edición',
      slicingMode: 'Modo de corte',
      pointAddingMode: 'Modo de añadir puntos',
      view: 'Vista',
      editVertices: 'Editar vértices',
      addPoints: 'Añadir puntos',
      slice: 'Cortar',
      createPolygon: 'Crear polígono',
      deletePolygon: 'Eliminar polígono',
    },
    totalPolygons: 'Total de polígonos',
    totalVertices: 'Total de vértices',
    completedSegmentation: 'Completado',
    mode: 'Modo',
    saveSuccess: 'Segmentación guardada con éxito',
    resegmentSuccess: 'Resegmentación iniciada con éxito',
    resegmentComplete: 'Resegmentación completada con éxito',
    resegmentError: 'Error al resegmentar la imagen',
    resegmentButton: 'Resegmentar',
    resegmentButtonTooltip: 'Ejecutar segmentación de nuevo en esta imagen',
    polygonDeleted: 'Polígono eliminado con éxito',
    vertices: 'Vértices',
    zoom: 'Zoom',
    position: 'Posición',
    selected: 'Seleccionado',
    none: 'Ninguno',
    polygons: 'Polígonos',
    noData: 'No hay datos de segmentación disponibles',
    noPolygons: 'No se encontraron polígonos',
    regions: 'Segmentaciones',
    helpTips: {
      title: 'Consejos:',
      edit: {
        createPoint: 'Haz clic para crear un nuevo punto',
        shiftPoints: 'Mantén presionada la tecla Shift para crear automáticamente una secuencia de puntos',
        closePolygon: 'Cierra el polígono haciendo clic en el primer punto',
      },
      slice: {
        start: 'Haz clic para comenzar a cortar',
        finish: 'Haz clic de nuevo para finalizar el corte',
        cancel: 'Presiona Esc para cancelar el corte',
      },
      addPoint: {
        hover: 'Pasa el cursor sobre una línea del polígono',
        click: 'Haz clic para añadir un punto al polígono seleccionado',
        exit: 'Presiona Esc para salir del modo de añadir puntos',
      },
    },
    title: 'Editor de segmentación',
    clickToAddPoint: 'Haz clic para añadir un punto',
    clickToCompletePolygon: 'Haz clic en el primer punto para completar el polígono',
    clickToAddFirstSlicePoint: 'Haz clic para añadir el primer punto de corte',
    clickToAddSecondSlicePoint: 'Haz clic para añadir el segundo punto de corte',
    polygonCreationMode: 'Modo de creación de polígono',
    polygonEditMode: 'Modo de edición de polígono',
    polygonSliceMode: 'Modo de corte de polígono',
    polygonAddPointsMode: 'Modo de añadir puntos',
    viewMode: 'Modo de vista',
  },
  termsPage: {
    title: 'Términos de Servicio',
    acceptance: {
      title: '1. Aceptación de los Términos',
      paragraph1:
        'Al acceder o utilizar SpheroSeg, aceptas estar sujeto a estos Términos de Servicio y a todas las leyes y regulaciones aplicables. Si no estás de acuerdo con alguno de estos términos, se te prohíbe usar este servicio.',
    },
    useLicense: {
      title: '2. Licencia de Uso',
      paragraph1:
        'Se concede permiso para utilizar temporalmente SpheroSeg solo para fines personales, no comerciales o de investigación académica. Esto es la concesión de una licencia, no una transferencia de título.',
    },
    dataUsage: {
      title: '3. Uso de Datos',
      paragraph1:
        'Cualquier dato subido a SpheroSeg sigue siendo de tu propiedad. No reclamamos la propiedad de tu contenido, pero requerimos ciertos permisos para proporcionar el servicio.',
    },
    limitations: {
      title: '4. Limitaciones',
      paragraph1:
        'En ningún caso SpheroSeg será responsable por cualquier daño que surja del uso o la imposibilidad de usar la plataforma, incluso si se nos ha notificado la posibilidad de dicho daño.',
    },
    revisions: {
      title: '5. Revisiones y Erratas',
      paragraph1:
        'Los materiales que aparecen en SpheroSeg podrían incluir errores técnicos, tipográficos o fotográficos. No garantizamos que ninguno de los materiales sea preciso, completo o actual.',
    },
    governingLaw: {
      title: '6. Ley Aplicable',
      paragraph1:
        'Estos términos y condiciones se rigen e interpretan de acuerdo con las leyes del país en el que se aloja el servicio, y te sometes irrevocablemente a la jurisdicción exclusiva de los tribunales de esa ubicación.',
    },
  },
  statsOverview: {
    totalProjects: 'Total de proyectos',
    totalProjectsDesc: '{count} nuevo(s) este mes',
    totalImages: 'Total de imágenes',
    totalImagesDesc: '{count, number}% de aumento',
    completedSegmentations: 'Segmentaciones completadas',
    completedSegmentationsDesc: '{count} más que el período anterior',
    segmentationsToday: 'Segmentaciones hoy',
    segmentationsTodayDesc: '+{count} vs ayer',
    fetchError: 'Error al cargar las estadísticas.',
    loadError: 'No se pudieron cargar las estadísticas.',
    title: 'Vista general del panel',
    storageUsed: 'Almacenamiento utilizado',
    recentActivity: 'Actividad reciente',
    moreStats: 'Ver estadísticas detalladas',
    completion: 'tasa de finalización',
    vsLastMonth: 'vs. mes pasado',
    thisMonth: 'Este mes',
    lastMonth: 'Mes pasado',
    projectsCreated: 'Proyectos creados',
    imagesUploaded: 'Imágenes subidas',
    storageLimit: 'Límite de almacenamiento',
    activityTitle: 'Actividad reciente',
    noActivity: 'No hay actividad reciente',
    activityTypes: {
      project_created: 'Proyecto creado',
      image_uploaded: 'Imagen subida',
      segmentation_completed: 'Segmentación completada',
    },
  },
  privacyPage: {
    title: 'Política de Privacidad',
    introduction: {
      title: '1. Introducción',
      paragraph1:
        'Esta Política de Privacidad explica cómo SpheroSeg ("nosotros", "nos", "nuestro") recopila, utiliza y comparte tu información cuando utilizas nuestra plataforma para la segmentación y análisis de esferoides.',
    },
    informationWeCollect: {
      title: '2. Información que Recopilamos',
      paragraph1:
        'Recopilamos información que nos proporcionas directamente cuando creas una cuenta, subes imágenes, creas proyectos y de otras formas interactúas con nuestros servicios.',
    },
    personalInformation: {
      title: '2.1 Información Personal',
      paragraph1:
        'Esto incluye tu nombre, dirección de correo electrónico, institución/organización y otra información que proporcionas al configurar tu cuenta o solicitar acceso a nuestros servicios.',
    },
    researchData: {
      title: '2.2 Datos de Investigación',
      paragraph1:
        'Esto incluye imágenes que subes, detalles del proyecto, resultados de análisis y otros datos relacionados con la investigación que creas o subes a nuestra plataforma.',
    },
    usageInformation: {
      title: '2.3 Información de Uso',
      paragraph1:
        'Recopilamos información sobre cómo utilizas nuestra plataforma, incluidos datos de registro, información del dispositivo y patrones de uso.',
    },
    howWeUse: {
      title: '3. Cómo Utilizamos tu Información',
      paragraph1:
        'Utilizamos la información que recopilamos para proporcionar, mantener y mejorar nuestros servicios, para comunicarnos contigo y para cumplir con nuestras obligaciones legales.',
    },
    dataSecurity: {
      title: '4. Seguridad de Datos',
      paragraph1:
        'Implementamos medidas de seguridad apropiadas para proteger tu información personal y datos de investigación contra acceso, alteración, divulgación o destrucción no autorizados.',
    },
    dataSharing: {
      title: '5. Compartición de Datos',
      paragraph1:
        'No vendemos tu información personal ni datos de investigación. Podemos compartir tu información en circunstancias limitadas, como con tu consentimiento, para cumplir con obligaciones legales o con proveedores de servicios que nos ayudan a operar nuestra plataforma.',
    },
    yourChoices: {
      title: '6. Tus Opciones',
      paragraph1:
        'Puedes acceder, actualizar o eliminar la información de tu cuenta y datos de investigación a través de la configuración de tu cuenta. También puedes contactarnos para solicitar acceso, corrección o eliminación de cualquier información personal que tengamos sobre ti.',
    },
    changes: {
      title: '7. Cambios en esta Política',
      paragraph1:
        'Podemos actualizar esta Política de Privacidad de vez en cuando. Te notificaremos de cualquier cambio publicando la nueva Política de Privacidad en esta página y actualizando la fecha de "Última Actualización".',
    },
    contactUs: {
      title: '8. Contáctanos',
      paragraph1:
        'Si tienes alguna pregunta sobre esta Política de Privacidad, por favor contá<NAME_EMAIL>.',
    },
    lastUpdated: 'Última Actualización: 1 de julio de 2023',
  },
  editor: {
    backButtonTooltip: 'Volver a la vista general del proyecto',
    exportButtonTooltip: 'Exportar los datos de segmentación actuales',
    saveTooltip: 'Guardar tus cambios',
    image: 'Imagen',
    previousImage: 'Imagen anterior',
    nextImage: 'Imagen siguiente',
    resegmentButton: 'Resegmentar',
    resegmentButtonTooltip: 'Ejecutar segmentación de nuevo en esta imagen',
    exportMaskButton: 'Exportar máscara',
    exportMaskButtonTooltip: 'Exportar la máscara de segmentación para esta imagen',
    backButton: 'Volver',
    exportButton: 'Exportar',
    saveButton: 'Guardar',
    loadingProject: 'Cargando proyecto...',
    loadingImage: 'Cargando imagen...',
    sliceErrorInvalidPolygon: 'No se puede cortar: polígono seleccionado no válido.',
    sliceWarningInvalidResult: 'El corte resultó en polígonos demasiado pequeños para ser válidos.',
    sliceWarningInvalidIntersections:
      'Corte no válido: la línea de corte debe intersecar el polígono en exactamente dos puntos.',
    sliceSuccess: 'Polígono cortado con éxito.',
    noPolygonToSlice: 'No hay polígonos disponibles para cortar.',
    savingTooltip: 'Guardando...',
  },
  segmentationPage: {
    noImageSelected: 'No se ha seleccionado ninguna imagen para resegmentación.',
    resegmentationStarted: 'Iniciando resegmentación con la red neuronal ResUNet...',
    resegmentationQueued: 'La resegmentación ha sido puesta en cola.',
    resegmentationCompleted: 'Resegmentación completada con éxito.',
    resegmentationFailed: 'La resegmentación falló.',
    resegmentationTimeout: 'La resegmentación agotó el tiempo de espera. Por favor, comprueba el estado de la cola.',
    resegmentationError: 'Error al iniciar la resegmentación.',
    resegmentTooltip: 'Resegmentar',
  },
  errors: {
    fetchSegmentationFailed: 'Error al cargar los datos de segmentación',
    fetchImageFailed: 'Error al cargar los datos de la imagen',
    saveSegmentationFailed: 'Error al guardar la segmentación',
    networkError: 'Se produjo un error de red',
    serverError: 'Se produjo un error del servidor',
    unknownError: 'Se produjo un error desconocido',
    imageNotFound: 'Imagen no encontrada',
    imageNotFoundDesc: 'La imagen a la que intentas acceder no existe o ha sido eliminada.',
    returnToProject: 'Volver al proyecto',
    goToDashboard: 'Ir al panel de control',
    somethingWentWrong: 'Algo salió mal',
    componentError: 'Ocurrió un error en este componente',
    goBack: 'Volver',
    tryAgain: 'Intentar de nuevo',
    notFound: 'Página no encontrada',
    unauthorized: 'Acceso no autorizado',
    forbidden: 'Acceso prohibido',
    timeoutError: 'La solicitud agotó el tiempo de espera',
    validationError: 'Error de validación',
    pageNotFound: 'Página no encontrada',
    pageNotFoundMessage: 'La página que solicitaste no pudo ser encontrada',
    goHome: 'Ir a la página de inicio',
  },
  project: {
    loading: 'Cargando proyecto...',
    notFound: 'Proyecto no encontrado',
    error: 'Error al cargar el proyecto',
    empty: 'Este proyecto está vacío',
    noImages: 'No se encontraron imágenes en este proyecto',
    addImages: 'Añade imágenes para comenzar',
  },
  navigation: {
    home: 'Inicio',
    projects: 'Proyectos',
    settings: 'Configuración',
    profile: 'Perfil',
    dashboard: 'Panel de control',
    back: 'Volver',
  },
  accessibility: {
    skipToContent: 'Saltar al contenido principal',
  },
  projectsPage: {
    title: 'Proyectos',
    description: 'Gestiona tus proyectos de investigación',
    createNew: 'Crear nuevo proyecto',
    createProject: 'Crear proyecto',
    createProjectDesc: 'Iniciar un nuevo proyecto de investigación',
    projectName: 'Nombre del proyecto',
    projectDescription: 'Descripción del proyecto',
    projectNamePlaceholder: 'Introduce el nombre del proyecto',
    projectDescriptionPlaceholder: 'Introduce la descripción del proyecto',
    projectCreated: 'Proyecto creado con éxito',
    projectCreationFailed: 'No se pudo crear el proyecto',
    projectDeleted: 'Proyecto eliminado con éxito',
    projectDeletionFailed: 'No se pudo eliminar el proyecto',
    confirmDelete: '¿Estás seguro de que quieres eliminar este proyecto?',
    confirmDeleteDescription:
      'Esta acción no se puede deshacer. Todos los datos asociados con este proyecto se eliminarán permanentemente.',
    deleteProject: 'Eliminar proyecto',
    editProject: 'Editar proyecto',
    viewProject: 'Ver proyecto',
    projectUpdated: 'Proyecto actualizado con éxito',
    projectUpdateFailed: 'No se pudo actualizar el proyecto',
    noProjects: 'No se encontraron proyectos',
    createFirstProject: 'Crea tu primer proyecto para empezar',
    searchProjects: 'Buscar proyectos...',
    filterProjects: 'Filtrar proyectos',
    sortProjects: 'Ordenar proyectos',
    projectNameRequired: 'El nombre del proyecto es obligatorio',
    loginRequired: 'Debes iniciar sesión para crear un proyecto',
    createdAt: 'Creado',
    updatedAt: 'Última actualización',
    imageCount: 'Imágenes',
    status: 'Estado',
    actions: 'Acciones',
    loading: 'Cargando proyectos...',
    error: 'Error al cargar proyectos',
    retry: 'Reintentar',
    duplicating: 'Duplicando proyecto...',
    duplicate: 'Duplicar',
    duplicateSuccess: 'Proyecto duplicado con éxito',
    duplicateFailed: 'No se pudo duplicar el proyecto',
    duplicateTitle: 'Duplicar proyecto',
    duplicateProject: 'Duplicar proyecto',
    duplicateProjectDescription:
      'Crea una copia de este proyecto incluyendo todas las imágenes. Puedes personalizar las opciones a continuación.',
    duplicateCancelled: 'Duplicación del proyecto cancelada',
    duplicatingProject: 'Duplicando proyecto',
    duplicatingProjectDescription: 'Tu proyecto está siendo duplicado. Esto puede tardar unos momentos.',
    duplicateProgress: 'Progreso de duplicación',
    duplicationComplete: 'Duplicación del proyecto completada',
    duplicationTaskFetchError: 'Error al obtener datos de la tarea',
    duplicationCancelError: 'Error al cancelar la duplicación',
    duplicateProgressDescription:
      'Tu proyecto está siendo duplicado. Este proceso puede llevar algo de tiempo para proyectos grandes.',
    duplicationPending: 'Pendiente',
    duplicationProcessing: 'Procesando',
    duplicationCompleted: 'Completado',
    duplicationFailed: 'Fallido',
    duplicationCancelled: 'Cancelado',
    duplicationCancellationFailed: 'No se pudo cancelar la duplicación',
    duplicationSuccessMessage: '¡Proyecto duplicado con éxito! Ahora puedes acceder al nuevo proyecto.',
    copySegmentations: 'Copiar resultados de segmentación',
    resetImageStatus: 'Restablecer estado de procesamiento de imágenes',
    newProjectTitle: 'Nuevo título de proyecto',
    itemsProcessed: 'elementos procesados',
    items: 'elementos',
    unknownProject: 'Proyecto desconocido',
    activeTasks: 'Activo',
    allTasks: 'Todos',
    noActiveDuplications: 'No hay duplicaciones activas',
    noDuplications: 'No se encontraron tareas de duplicación',
    deleteProjectDescription: 'Esta acción eliminará permanentemente el proyecto y todos los datos asociados.',
    deleteWarning:
      'Esta acción no se puede deshacer. Todos los datos asociados con este proyecto se eliminarán permanentemente.',
    untitledProject: 'Proyecto sin título',
    typeToConfirm: 'Escribe "delete" para confirmar',
    deleteConfirm: '¿Estás seguro de que quieres eliminar este proyecto?',
    exportProject: 'Exportar proyecto',
    archived: 'Archivado',
    completed: 'Completado',
    draft: 'Borrador',
    active: 'Activo',
    createDate: 'Creado',
    lastModified: 'Última modificación',
    projectDescPlaceholder: 'Introduce la descripción del proyecto',
    creatingProject: 'Creando proyecto...',
  },
};
