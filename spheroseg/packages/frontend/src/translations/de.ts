export default {
  project: {
    detail: {
      noImagesSelected: 'Keine Bilder ausgewählt',
      triggeringResegmentation: 'Neusegmentierung für {{count}} Bilder wird ausgelöst...',
      deleteConfirmation: 'Sind Sie sicher, dass Sie {{count}} Bilder löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.',
      deletingImages: '{{count}} Bilder werden gelöscht...',
      deleteSuccess: '{{count}} Bilder erfolgreich gelöscht',
      deleteFailed: 'Fehler beim Löschen von {{count}} Bildern',
      preparingExport: 'Export von {{count}} Bildern wird vorbereitet...'
    },
    segmentation: {
      processingInBatches: 'Segmentierung für {{count}} Bilder in {{batches}} Stapeln wird gestartet...',
      batchQueued: 'Stapel {{current}}/{{total}} erfolgreich in die Warteschlange gestellt',
      batchQueuedFallback: 'Stapel {{current}}/{{total}} erfolgreich in die Warteschlange gestellt (Fallback-Endpunkt)',
      batchError: 'Fehler bei der Verarbeitung von Stapel {{current}}/{{total}}',
      partialSuccess: 'Segmentierung: {{success}} Bilder erfolgreich in die Warteschlange gestellt, {{failed}} fehlgeschlagen',
      allSuccess: 'Segmentierung: Alle {{count}} Bilder erfolgreich in die Warteschlange gestellt',
      allFailed: 'Segmentierung: Alle {{count}} Bilder fehlgeschlagen',
      startedImages: 'Segmentierung für {{count}} Bilder gestartet',
      queuedLocallyWarning: 'Segmentierung für {{count}} Bilder lokal in die Warteschlange gestellt. Serververbindung fehlgeschlagen.'
    },
    noImages: {
      title: 'Noch keine Bilder',
      description: 'Dieses Projekt enthält noch keine Bilder. Laden Sie Bilder hoch, um mit der Segmentierung zu beginnen.',
      uploadButton: 'Bilder hochladen',
    },
    loading: 'Projekt wird geladen...',
    notFound: 'Projekt nicht gefunden',
    error: 'Fehler beim Laden des Projekts',
    empty: 'Dieses Projekt ist leer',
    // noImages key already exists under project.noImages, this one is a general string for a project without images.
    // For clarity, if this 'noImages' key is meant to be a string value for `project.noImages`, it might be an error in the source structure.
    // Assuming it's distinct as `project.noImagesFoundString` or similar, but following the structure:
    // If `project: { noImages: 'No images found in this project' }` is intended:
    // noImages: 'Keine Bilder in diesem Projekt gefunden',
    // However, the structure has `project.noImages` as an object.
    // This particular "noImages" appears later in the input under a root `project` key which is distinct from the first `project` key.
    // This will be handled when that specific `project` key (at root level, not the one with `detail` and `segmentation`) is processed.
    addImages: 'Fügen Sie Bilder hinzu, um zu beginnen', // This also seems to belong to the later, root-level 'project' key.
  },
  projectsPage: {
    title: 'Projekte',
    description: 'Forschungsprojekte verwalten',
    createNew: 'Neues Projekt erstellen',
    createProject: 'Projekt erstellen',
    createProjectDesc: 'Ein neues Forschungsprojekt starten',
    projectName: 'Projektname',
    projectDescription: 'Projektbeschreibung',
    projectNamePlaceholder: 'Projektnamen eingeben',
    projectDescriptionPlaceholder: 'Projektbeschreibung eingeben',
    projectCreated: 'Projekt erfolgreich erstellt',
    projectCreationFailed: 'Projekterstellung fehlgeschlagen',
    projectDeleted: 'Projekt erfolgreich gelöscht',
    projectDeletionFailed: 'Projektlöschung fehlgeschlagen',
    confirmDelete: 'Sind Sie sicher, dass Sie dieses Projekt löschen möchten?',
    confirmDeleteDescription: 'Diese Aktion kann nicht rückgängig gemacht werden. Alle mit diesem Projekt verbundenen Daten werden dauerhaft gelöscht.',
    deleteProject: 'Projekt löschen',
    editProject: 'Projekt bearbeiten',
    viewProject: 'Projekt ansehen',
    projectUpdated: 'Projekt erfolgreich aktualisiert',
    projectUpdateFailed: 'Projektaktualisierung fehlgeschlagen',
    noProjects: 'Keine Projekte gefunden',
    createFirstProject: 'Erstellen Sie Ihr erstes Projekt, um zu beginnen',
    searchProjects: 'Projekte suchen...',
    filterProjects: 'Projekte filtern',
    sortProjects: 'Projekte sortieren',
    projectNameRequired: 'Projektname ist erforderlich',
    loginRequired: 'Sie müssen angemeldet sein, um ein Projekt zu erstellen',
    createdAt: 'Erstellt am',
    updatedAt: 'Zuletzt aktualisiert',
    imageCount: 'Bilder',
    status: 'Status',
    actions: 'Aktionen',
    loading: 'Projekte werden geladen...',
    error: 'Fehler beim Laden der Projekte',
    retry: 'Erneut versuchen',
    duplicating: 'Projekt wird dupliziert...',
    duplicate: 'Duplizieren',
    duplicateSuccess: 'Projekt erfolgreich dupliziert',
    duplicateFailed: 'Projektduplizierung fehlgeschlagen',
    duplicateTitle: 'Projekt duplizieren',
    duplicateProject: 'Projekt duplizieren',
    duplicateProjectDescription: 'Erstellen Sie eine Kopie dieses Projekts einschließlich aller Bilder. Sie können die Optionen unten anpassen.',
    duplicateCancelled: 'Projektduplizierung abgebrochen',
    duplicatingProject: 'Projekt wird dupliziert',
    duplicatingProjectDescription: 'Ihr Projekt wird dupliziert. Dies kann einige Augenblicke dauern.',
    duplicateProgress: 'Duplizierungsfortschritt',
    duplicationComplete: 'Projektduplizierung abgeschlossen',
    duplicationTaskFetchError: 'Fehler beim Abrufen der Aufgabendaten',
    duplicationCancelError: 'Fehler beim Abbrechen der Duplizierung',
    duplicateProgressDescription: 'Ihr Projekt wird dupliziert. Dieser Vorgang kann bei großen Projekten einige Zeit dauern.',
    duplicationPending: 'Ausstehend',
    duplicationProcessing: 'In Bearbeitung',
    duplicationCompleted: 'Abgeschlossen',
    duplicationFailed: 'Fehlgeschlagen',
    duplicationCancelled: 'Abgebrochen',
    duplicationCancellationFailed: 'Fehler beim Abbrechen der Duplizierung',
    duplicationSuccessMessage: 'Projekt erfolgreich dupliziert! Sie können jetzt auf das neue Projekt zugreifen.',
    copySegmentations: 'Segmentierungsergebnisse kopieren',
    resetImageStatus: 'Bildverarbeitungsstatus zurücksetzen',
    newProjectTitle: 'Neuer Projekttitel',
    itemsProcessed: 'Elemente verarbeitet',
    items: 'Elemente',
    unknownProject: 'Unbekanntes Projekt',
    activeTasks: 'Aktive',
    allTasks: 'Alle',
    noActiveDuplications: 'Keine aktiven Duplizierungen',
    noDuplications: 'Keine Duplizierungsaufgaben gefunden',
    deleteProjectDescription: 'Diese Aktion löscht das Projekt und alle zugehörigen Daten dauerhaft.',
    deleteWarning: 'Diese Aktion kann nicht rückgängig gemacht werden. Alle mit diesem Projekt verbundenen Daten werden dauerhaft gelöscht.',
    untitledProject: 'Unbenanntes Projekt',
    typeToConfirm: 'Geben Sie "löschen" ein, um zu bestätigen',
    deleteConfirm: 'Sind Sie sicher, dass Sie dieses Projekt löschen möchten?',
    exportProject: 'Projekt exportieren',
    archived: 'Archiviert',
    completed: 'Abgeschlossen',
    draft: 'Entwurf',
    active: 'Aktiv',
    createDate: 'Erstellt am',
    lastModified: 'Zuletzt geändert',
    projectDescPlaceholder: 'Projektbeschreibung eingeben',
    creatingProject: 'Projekt wird erstellt...',
  },
  common: {
    appName: 'Sphäroidsegmentierung',
    appNameShort: 'SpheroSeg',
    loading: 'Wird geladen...',
    save: 'Speichern',
    cancel: 'Abbrechen',
    delete: 'Löschen',
    edit: 'Bearbeiten',
    create: 'Erstellen',
    search: 'Suchen',
    error: 'Fehler',
    success: 'Erfolg',
    reset: 'Zurücksetzen',
    clear: 'Leeren',
    back: 'Zurück',
    signIn: 'Anmelden',
    signUp: 'Registrieren',
    signOut: 'Abmelden',
    signingIn: 'Anmeldung läuft...',
    settings: 'Einstellungen',
    profile: 'Profil',
    dashboard: 'Dashboard',
    project: 'Projekt',
    projects: 'Projekte',
    newProject: 'Neues Projekt',
    upload: 'Hochladen',
    download: 'Herunterladen',
    removeAll: 'Alle entfernen',
    uploadImages: 'Bilder hochladen',
    recentAnalyses: 'Letzte Analysen',
    noProjects: 'Keine Projekte gefunden',
    noImages: 'Keine Bilder gefunden',
    createYourFirst: 'Erstellen Sie Ihr erstes Projekt, um zu beginnen',
    tryAgain: 'Erneut versuchen',
    email: 'E-Mail',
    password: 'Passwort',
    confirmPassword: 'Passwort bestätigen',
    firstName: 'Vorname',
    lastName: 'Nachname',
    username: 'Benutzername',
    name: 'Name',
    description: 'Beschreibung',
    date: 'Datum',
    status: 'Status',
    image: 'Bild',
    projectName: 'Projektname',
    projectDescription: 'Projektbeschreibung',
    language: 'Sprache',
    theme: 'Theme',
    light: 'Hell',
    dark: 'Dunkel',
    system: 'System',
    welcome: 'Willkommen auf der Sphäroidsegmentierungs-Plattform',
    account: 'Konto',
    notifications: 'Benachrichtigungen',
    passwordConfirm: 'Passwort bestätigen',
    manageAccount: 'Konto verwalten',
    changePassword: 'Passwort ändern',
    deleteAccount: 'Konto löschen',
    requestAccess: 'Zugriff anfordern',
    accessRequest: 'Zugriffsanfrage',
    createAccount: 'Konto erstellen',
    signInToAccount: 'Bei Konto anmelden',
    termsOfService: 'Nutzungsbedingungen',
    privacyPolicy: 'Datenschutzrichtlinie',
    termsOfServiceLink: 'Nutzungsbedingungen',
    privacyPolicyLink: 'Datenschutzrichtlinie',
    optional: 'Optional',
    saveChanges: 'Änderungen speichern',
    saving: 'Wird gespeichert...',
    notSpecified: 'Nicht angegeben',
    enable: 'Aktivieren',
    disable: 'Deaktivieren',
    backToHome: 'Zurück zur Startseite',
    and: 'und',
    lastChange: 'Letzte Änderung',
    sort: 'Sortieren',
    emailPlaceholder: 'Ihre E-Mail-Adresse eingeben',
    passwordPlaceholder: 'Ihr Passwort eingeben',
    export: 'Exportieren',
    selectImages: 'Bilder auswählen',
    noImagesDescription: 'Laden Sie Bilder hoch, um mit Ihrem Projekt zu beginnen',
    yes: 'Ja',
    no: 'Nein',
    images: 'Bilder',
    files: 'Dateien',
    validationFailed: 'Validierung fehlgeschlagen',
    cropAvatar: 'Profilbild zuschneiden',
    profileTitle: 'Profil',
    profileDescription: 'Aktualisieren Sie Ihre Profilinformationen, die für andere Benutzer sichtbar sind',
    profileUsername: 'Benutzername',
    profileUsernamePlaceholder: 'Benutzernamen eingeben',
    profileFullName: 'Vollständiger Name',
    profileFullNamePlaceholder: 'Vollständigen Namen eingeben',
    profileTitlePlaceholder: 'z.B. Forscher, Professor',
    profileOrganization: 'Organisation',
    profileOrganizationPlaceholder: 'Ihre Organisation oder Institution eingeben',
    profileBio: 'Bio',
    profileBioPlaceholder: 'Schreiben Sie eine kurze Biografie über sich',
    profileBioDescription: 'Kurze Beschreibung Ihrer Forschungsinteressen und Fachkenntnisse',
    profileLocation: 'Standort',
    profileLocationPlaceholder: 'z.B. Prag, Tschechische Republik',
    profileSaveButton: 'Profil speichern',
    actions: 'Aktionen',
    view: 'Ansehen',
    share: 'Teilen',
    projectNamePlaceholder: 'Projektnamen eingeben',
    projectDescPlaceholder: 'Projektbeschreibung eingeben',
    creatingProject: 'Projekt wird erstellt...',
    createSuccess: 'Projekt erfolgreich erstellt',
    unauthorized: 'Sie sind nicht berechtigt, diese Aktion auszuführen',
    forbidden: 'Zugriff verboten',
    maxFileSize: 'Maximale Dateigröße: {size}MB',
    accepted: 'Akzeptiert',
    processing: 'Verarbeite...',
    uploading: 'Hochladen...',
    uploadComplete: 'Upload abgeschlossen',
    uploadFailed: 'Upload fehlgeschlagen',
    deletePolygon: 'Polygon löschen',
    editor: {
      error: 'Fehler',
      success: 'Erfolg',
      edit: 'Bearbeiten',
      create: 'Erstellen',
    },
  },
  // This is the root-level `project` key mentioned in comments during thought process.
  // It's distinct from the first `project` key that contains `detail`, `segmentation`, `noImages` (object).
  project: {
    loading: 'Projekt wird geladen...',
    notFound: 'Projekt nicht gefunden',
    error: 'Fehler beim Laden des Projekts',
    empty: 'Dieses Projekt ist leer',
    noImages: 'Keine Bilder in diesem Projekt gefunden', // This is a string value, different from `project.noImages` (object) earlier
    addImages: 'Fügen Sie Bilder hinzu, um zu beginnen',
  },
  auth: {
    signIn: 'Anmelden',
    signUp: 'Registrieren',
    signOut: 'Abmelden',
    signingIn: 'Anmeldung läuft...',
    forgotPassword: 'Passwort vergessen?',
    resetPassword: 'Passwort zurücksetzen',
    dontHaveAccount: 'Sie haben noch kein Konto?',
    alreadyHaveAccount: 'Sie haben bereits ein Konto?',
    createAccount: 'Konto erstellen',
    signInWithGoogle: 'Mit Google anmelden',
    signInWithGithub: 'Mit GitHub anmelden',
    or: 'oder',
    signInTitle: 'Anmelden',
    signInDescription: 'Melden Sie sich bei Ihrem Konto an',
    noAccount: 'Sie haben noch kein Konto?',
    emailAddressLabel: 'E-Mail-Adresse',
    passwordLabel: 'Passwort',
    currentPasswordLabel: 'Aktuelles Passwort',
    newPasswordLabel: 'Neues Passwort',
    confirmPasswordLabel: 'Passwort bestätigen',
    rememberMe: 'Angemeldet bleiben',
    emailRequired: 'E-Mail ist erforderlich',
    passwordRequired: 'Passwort ist erforderlich',
    alreadyLoggedInTitle: 'Sie sind bereits angemeldet',
    alreadyLoggedInMessage: 'Sie sind bereits bei Ihrem Konto angemeldet',
    goToDashboardLink: 'Zum Dashboard gehen',
    invalidEmail: 'Ungültige E-Mail-Adresse',
    passwordTooShort: 'Passwort muss mindestens 6 Zeichen lang sein',
    passwordsDontMatch: 'Passwörter stimmen nicht überein',
    invalidCredentials: 'Ungültige E-Mail-Adresse oder ungültiges Passwort',
    accountCreated: 'Konto erfolgreich erstellt',
    resetLinkSent: 'Link zum Zurücksetzen des Passworts an Ihre E-Mail-Adresse gesendet',
    resetSuccess: 'Passwort erfolgreich zurückgesetzt',
    signInSuccess: 'Erfolgreich angemeldet',
    signOutSuccess: 'Erfolgreich abgemeldet',
    sessionExpired: 'Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an.',
    unauthorized: 'Sie sind nicht berechtigt, auf diese Ressource zuzugreifen',
    verifyEmail: 'Bitte bestätigen Sie Ihre E-Mail-Adresse',
    verificationLinkSent: 'Bestätigungslink an Ihre E-Mail-Adresse gesendet',
    verificationSuccess: 'E-Mail erfolgreich bestätigt',
    resendVerification: 'Bestätigungs-E-Mail erneut senden',
    requestAccess: 'Zugriff anfordern',
    termsAndPrivacy: 'Durch die Registrierung stimmen Sie unseren Nutzungsbedingungen und Datenschutzrichtlinien zu.',
    forgotPasswordLink: 'Passwort vergessen?',
    passwordChanged: 'Passwort erfolgreich geändert',
    currentPasswordIncorrect: 'Aktuelles Passwort ist falsch',
    registerTitle: 'Konto erstellen',
    registerDescription: 'Registrieren Sie sich für ein neues Konto',
    registerSuccess: 'Registrierung erfolgreich! Sie können sich jetzt anmelden.',
    emailPlaceholder: 'Ihre E-Mail-Adresse eingeben',
    passwordPlaceholder: 'Ihr Passwort eingeben',
    firstNamePlaceholder: 'z.B. Max',
    lastNamePlaceholder: 'z.B. Mustermann',
    passwordConfirmPlaceholder: 'Bestätigen Sie Ihr Passwort',
    signUpTitle: 'Konto erstellen',
    signUpDescription: 'Registrieren Sie sich für ein neues Konto',
    enterInfoCreateAccount: 'Geben Sie Ihre Informationen ein, um ein Konto zu erstellen',
    creatingAccount: 'Konto wird erstellt...',
    alreadyHaveAccess: 'Bereits Zugriff?',
    forgotPasswordTitle: 'Passwort zurücksetzen',
    checkYourEmail: 'Überprüfen Sie Ihre E-Mails auf einen Link zum Zurücksetzen',
    enterEmailForReset: 'Geben Sie Ihre E-Mail-Adresse ein und wir senden Ihnen einen Link zum Zurücksetzen des Passworts',
    passwordResetLinkSent: 'Wenn ein Konto für diese E-Mail existiert, wurde ein Link zum Zurücksetzen des Passworts gesendet',
    passwordResetFailed: 'Fehler beim Senden des Links zum Zurücksetzen des Passworts. Bitte versuchen Sie es erneut.',
    enterEmail: 'Bitte geben Sie Ihre E-Mail-Adresse ein',
    sendingResetLink: 'Link zum Zurücksetzen wird gesendet...',
    sendResetLink: 'Link zum Zurücksetzen senden',
    backToSignIn: 'Zurück zum Anmelden',
  },
  requestAccess: {
    and: 'und',
    title: 'Zugriff auf die Sphäroidsegmentierungs-Plattform anfordern',
    description: 'Füllen Sie das folgende Formular aus, um Zugriff auf unsere Plattform anzufordern. Wir werden Ihre Anfrage prüfen und uns bald bei Ihnen melden.',
    emailLabel: 'Ihre E-Mail-Adresse',
    nameLabel: 'Ihr Name',
    institutionLabel: 'Institution/Unternehmen',
    reasonLabel: 'Grund für den Zugriff',
    submitRequest: 'Anfrage senden',
    requestReceived: 'Anfrage erhalten',
    thankYou: 'Vielen Dank für Ihr Interesse',
    weWillContact: 'Wir werden Ihre Anfrage prüfen und uns bald bei Ihnen melden',
    submitSuccess: 'Anfrage erfolgreich gesendet!',
    emailPlaceholder: 'Ihre E-Mail-Adresse eingeben',
    namePlaceholder: 'Ihren vollständigen Namen eingeben',
    institutionPlaceholder: 'Name Ihrer Institution oder Ihres Unternehmens eingeben',
    reasonPlaceholder: 'Bitte beschreiben Sie, wie Sie die Plattform nutzen möchten',
    fillRequired: 'Bitte füllen Sie alle Pflichtfelder aus',
    submittingRequest: 'Anfrage wird gesendet...',
    submitError: 'Fehler beim Senden der Anfrage',
    alreadyPending: 'Eine Zugriffsanfrage für diese E-Mail ist bereits anhängig',
    agreeToTerms: 'Indem Sie diese Anfrage senden, stimmen Sie unseren zu',
  },
  requestAccessForm: {
    title: 'Zugriff auf die Sphäroidsegmentierungs-Plattform anfordern',
    description: 'Füllen Sie das folgende Formular aus, um Zugriff auf unsere Plattform anzufordern. Wir werden Ihre Anfrage prüfen und uns bald bei Ihnen melden.',
    emailLabel: 'Ihre E-Mail-Adresse',
    nameLabel: 'Ihr Name',
    institutionLabel: 'Institution/Unternehmen',
    reasonLabel: 'Grund für den Zugriff',
    submitButton: 'Anfrage senden',
    signInPrompt: 'Sie haben bereits ein Konto?',
    signInLink: 'Anmelden',
    thankYouTitle: 'Vielen Dank für Ihr Interesse',
    weWillContact: 'Wir werden Ihre Anfrage prüfen und uns bald bei Ihnen melden',
    agreeToTerms: 'Indem Sie diese Anfrage senden, stimmen Sie unseren zu',
    and: 'und',
  },
  documentation: {
    tag: 'Benutzerhandbuch',
    title: 'SpheroSeg Dokumentation',
    subtitle: 'Erfahren Sie, wie Sie die Sphäroidsegmentierungs-Plattform effektiv nutzen.',
    sidebar: {
      title: 'Abschnitte',
      introduction: 'Einführung',
      gettingStarted: 'Erste Schritte',
      uploadingImages: 'Bilder hochladen',
      segmentationProcess: 'Segmentierungsprozess',
      apiReference: 'API-Referenz',
    },
    introduction: {
      title: 'Einführung',
      imageAlt: 'Illustration des Sphäroidanalyse-Workflows',
      whatIs: {
        title: 'Was ist SpheroSeg?',
        paragraph1: 'SpheroSeg ist eine hochmoderne Plattform für die Segmentierung und Analyse von Zellsphäroiden in mikroskopischen Bildern. Unser Tool bietet Forschern präzise Erkennungs- und Analysefunktionen.',
        paragraph2: 'Es verwendet fortschrittliche KI-Algorithmen auf Basis von Deep Learning, um Sphäroide in Ihren Bildern automatisch mit hoher Genauigkeit und Konsistenz zu identifizieren und zu segmentieren.',
        paragraph3: 'Diese Dokumentation führt Sie durch alle Aspekte der Nutzung der Plattform, von den ersten Schritten bis hin zu erweiterten Funktionen und der API-Integration.',
      },
    },
    gettingStarted: {
      title: 'Erste Schritte',
      accountCreation: {
        title: 'Kontoerstellung',
        paragraph1: 'Um SpheroSeg nutzen zu können, müssen Sie ein Konto erstellen. Dies ermöglicht es uns, Ihre Projekte und Bilder sicher zu speichern.',
        step1Prefix: 'Besuchen Sie die',
        step1Link: 'Registrierungsseite',
        step2: 'Geben Sie Ihre institutionelle E-Mail-Adresse ein und erstellen Sie ein Passwort',
        step3: 'Vervollständigen Sie Ihr Profil mit Ihrem Namen und Ihrer Institution',
        step4: 'Bestätigen Sie Ihre E-Mail-Adresse über den Link, der an Ihren Posteingang gesendet wurde',
      },
      creatingProject: {
        title: 'Ihr erstes Projekt erstellen',
        paragraph1: 'Projekte helfen Ihnen, Ihre Arbeit zu organisieren. Jedes Projekt kann mehrere Bilder und deren entsprechende Segmentierungsergebnisse enthalten.',
        step1: 'Klicken Sie in Ihrem Dashboard auf "Neues Projekt"',
        step2: 'Geben Sie einen Projektnamen und eine Beschreibung ein',
        step3: 'Wählen Sie den Projekttyp (Standard: Sphäroidanalyse)',
        step4: 'Klicken Sie auf "Projekt erstellen", um fortzufahren',
      },
    },
    uploadingImages: {
      title: 'Bilder hochladen',
      paragraph1: 'SpheroSeg unterstützt verschiedene Bildformate, die üblicherweise in der Mikroskopie verwendet werden, einschließlich TIFF, PNG und JPEG.',
      methods: {
        title: 'Upload-Methoden',
        paragraph1: 'Es gibt mehrere Möglichkeiten, Bilder hochzuladen:',
        step1: 'Dateien per Drag & Drop direkt in den Upload-Bereich ziehen',
        step2: 'Klicken Sie auf den Upload-Bereich, um Dateien von Ihrem Computer zu durchsuchen und auszuwählen',
        step3: 'Mehrere Bilder gleichzeitig im Stapel hochladen',
      },
      note: {
        prefix: 'Hinweis:',
        text: 'Für optimale Ergebnisse stellen Sie sicher, dass Ihre Mikroskopiebilder einen guten Kontrast zwischen Sphäroid und Hintergrund aufweisen.',
      },
    },
    segmentationProcess: {
      title: 'Segmentierungsprozess',
      paragraph1: 'Der Segmentierungsprozess identifiziert die Grenzen von Sphäroiden in Ihren Bildern und ermöglicht eine präzise Analyse ihrer Morphologie.',
      automatic: {
        title: 'Automatische Segmentierung',
        paragraph1: 'Unsere KI-gestützte automatische Segmentierung kann Sphäroidgrenzen mit hoher Genauigkeit erkennen:',
        step1: 'Wählen Sie ein Bild aus Ihrem Projekt aus',
        step2: 'Klicken Sie auf "Automatisch segmentieren", um den Vorgang zu starten',
        step3: 'Das System verarbeitet das Bild und zeigt die erkannten Grenzen an',
        step4: 'Überprüfen Sie die Ergebnisse im Segmentierungseditor',
      },
      manual: {
        title: 'Manuelle Anpassungen',
        paragraph1: 'Manchmal erfordert die automatische Segmentierung eine Verfeinerung. Unser Editor bietet Werkzeuge für:',
        step1: 'Hinzufügen oder Entfernen von Vertices entlang der Grenze',
        step2: 'Anpassen der Vertex-Positionen für genauere Grenzen',
        step3: 'Regionen teilen oder zusammenführen',
        step4: 'Löcher innerhalb von Sphäroiden hinzufügen oder entfernen',
      },
    },
    apiReference: {
      title: 'API-Referenz',
      paragraph1: 'SpheroSeg bietet eine RESTful-API für den programmatischen Zugriff auf die Funktionen der Plattform. Dies ist ideal für die Integration in Ihre bestehenden Arbeitsabläufe oder die Stapelverarbeitung.',
      endpoint1Desc: 'Ruft eine Liste all Ihrer Projekte ab',
      endpoint2Desc: 'Ruft alle Bilder innerhalb eines bestimmten Projekts ab',
      endpoint3Desc: 'Startet die Segmentierung für ein bestimmtes Bild',
      contactPrefix: 'Für die vollständige API-Dokumentation und Authentifizierungsdetails kontaktieren Sie uns bitte unter',
    },
    backToHome: 'Zurück zur Startseite',
    backToTop: 'Nach oben',
  },
  hero: {
    platformTag: 'Fortschrittliche Sphäroidsegmentierungs-Plattform',
    title: 'KI-gestützte Zellanalyse für die biomedizinische Forschung',
    subtitle: 'Verbessern Sie Ihre mikroskopische Zellbildanalyse mit unserer hochmodernen Sphäroidsegmentierungs-Plattform. Entwickelt für Forscher, die Präzision und Effizienz suchen.',
    getStartedButton: 'Loslegen',
    learnMoreButton: 'Mehr erfahren',
    imageAlt1: 'Mikroskopiebild eines Sphäroids',
    imageAlt2: 'Mikroskopiebild eines Sphäroids mit Analyse',
    welcomeTitle: 'Willkommen bei SpheroSeg',
    welcomeSubtitle: 'Fortschrittliche Plattform für die Segmentierung und Analyse von Zellsphäroiden',
    welcomeDescription: 'Unsere Plattform kombiniert modernste Algorithmen der künstlichen Intelligenz mit einer intuitiven Benutzeroberfläche für die präzise Erkennung und Analyse von Zellsphäroiden in mikroskopischen Bildern.',
    featuresTitle: 'Leistungsstarke Funktionen',
    featuresSubtitle: 'Fortschrittliche Werkzeuge für die biomedizinische Forschung',
    featureAiSegmentation: 'Fortschrittliche Segmentierung',
    featureAiSegmentationDesc: 'Präzise Sphäroiderkennung mit Grenzanalyse für genaue Zellmessungen.',
    featureEditing: 'KI-gestützte Analyse',
    featureEditingDesc: 'Nutzen Sie Deep-Learning-Algorithmen für die automatisierte Erkennung und Zellklassifizierung.',
    featureAnalytics: 'Einfaches Hochladen',
    featureAnalyticsDesc: 'Ziehen Sie Ihre Mikroskopiebilder per Drag & Drop für eine sofortige Verarbeitung und Analyse.',
    featureExport: 'Statistische Einblicke',
    featureExportDesc: 'Umfassende Metriken und Visualisierungen zur Gewinnung aussagekräftiger Datenmuster.',
    ctaTitle: 'Bereit, Ihren Zellanalyse-Workflow zu transformieren?',
    ctaSubtitle: 'Schließen Sie sich führenden Forschern an, die unsere Plattform bereits nutzen, um ihre Entdeckungen zu beschleunigen.',
    ctaButton: 'Konto erstellen',
  },
  navbar: {
    home: 'Startseite',
    features: 'Funktionen',
    documentation: 'Dokumentation',
    terms: 'Bedingungen',
    privacy: 'Datenschutz',
    login: 'Anmelden',
    requestAccess: 'Zugriff anfordern',
  },
  navigation: {
    home: 'Startseite',
    projects: 'Projekte',
    settings: 'Einstellungen',
    profile: 'Profil',
    dashboard: 'Dashboard',
    back: 'Zurück',
  },
  dashboard: {
    manageProjects: 'Verwalten und organisieren Sie Ihre Forschungsprojekte',
    viewMode: {
      grid: 'Rasteransicht',
      list: 'Listenansicht',
    },
    sort: {
      name: 'Name',
      updatedAt: 'Zuletzt aktualisiert',
      segmentationStatus: 'Status',
    },
    search: 'Projekte suchen...',
    searchImagesPlaceholder: 'Bilder suchen...',
    noProjects: 'Keine Projekte gefunden',
    noImagesDescription: 'Keine Bilder entsprechen Ihren Suchkriterien',
    createFirst: 'Erstellen Sie Ihr erstes Projekt, um zu beginnen',
    createNew: 'Neues Projekt erstellen',
    lastChange: 'Letzte Änderung',
    statsOverview: 'Statistikübersicht',
    totalProjects: 'Gesamtprojekte',
    activeProjects: 'Aktive Projekte',
    totalImages: 'Gesamtbilder',
    totalAnalyses: 'Gesamtanalysen',
    lastUpdated: 'Zuletzt aktualisiert',
    noProjectsDescription: 'Sie haben noch keine Projekte erstellt. Erstellen Sie Ihr erstes Projekt, um zu beginnen.',
    searchProjectsPlaceholder: 'Projekte nach Namen suchen...',
    sortBy: 'Sortieren nach',
    name: 'Name',
    completed: 'Abgeschlossen',
    processing: 'In Bearbeitung',
    pending: 'Ausstehend',
    failed: 'Fehlgeschlagen',
    selectImagesButton: 'Bilder auswählen',
  },
  projects: {
    title: 'Projekte',
    description: 'Verwalten Sie Ihre Forschungsprojekte',
    createNew: 'Neues Projekt erstellen',
    createProject: 'Projekt erstellen',
    createProjectDesc: 'Erstellen Sie ein neues Projekt, um mit Bildern und Segmentierung zu beginnen.',
    projectName: 'Projektname',
    projectDescription: 'Projektbeschreibung',
    projectNamePlaceholder: 'Projektnamen eingeben',
    projectDescriptionPlaceholder: 'Projektbeschreibung eingeben',
    projectDescPlaceholder: 'Projektbeschreibung eingeben',
    createDate: 'Erstellt am',
    creatingProject: 'Projekt wird erstellt...',
    createSuccess: 'Projekt erfolgreich erstellt',
    createError: 'Fehler beim Erstellen des Projekts',
    lastModified: 'Zuletzt geändert',
    projectCreated: 'Projekt erfolgreich erstellt',
    projectCreationFailed: 'Projekterstellung fehlgeschlagen',
    projectDeleted: 'Projekt erfolgreich gelöscht',
    deleteSuccess: 'Projekt erfolgreich gelöscht',
    deleteFailed: 'Fehler beim Löschen des Projekts',
    deleting: 'Projekt wird gelöscht...',
    notFound: 'Projekt nicht gefunden. Es wurde möglicherweise bereits gelöscht.',
    missingId: 'Projekt kann nicht gelöscht werden: Projektkennung fehlt',
    projectDeletionFailed: 'Projektlöschung fehlgeschlagen',
    confirmDelete: 'Sind Sie sicher, dass Sie dieses Projekt löschen möchten?',
    confirmDeleteDescription: 'Diese Aktion kann nicht rückgängig gemacht werden. Alle mit diesem Projekt verbundenen Daten werden dauerhaft gelöscht.',
    delete: 'Löschen',
    deleteProject: 'Projekt löschen',
    deleteProjectDescription: 'Diese Aktion kann nicht rückgängig gemacht werden. Dies löscht das Projekt und alle zugehörigen Daten dauerhaft.',
    deleteWarning: 'Sie sind dabei, das folgende Projekt zu löschen:',
    typeToConfirm: 'Geben Sie den Projektnamen ein, um zu bestätigen',
    confirmDeleteError: 'Bitte geben Sie den Projektnamen genau ein, um zu bestätigen',
    editProject: 'Projekt bearbeiten',
    viewProject: 'Projekt ansehen',
    projectUpdated: 'Projekt erfolgreich aktualisiert',
    projectUpdateFailed: 'Projektaktualisierung fehlgeschlagen',
    noProjects: 'Keine Projekte gefunden',
    createFirstProject: 'Erstellen Sie Ihr erstes Projekt, um zu beginnen',
    searchProjects: 'Projekte suchen...',
    filterProjects: 'Projekte filtern',
    sortProjects: 'Projekte sortieren',
    projectNameRequired: 'Projektname ist erforderlich',
    loginRequired: 'Sie müssen angemeldet sein, um ein Projekt zu erstellen',
    createdAt: 'Erstellt am',
    updatedAt: 'Zuletzt aktualisiert',
    imageCount: 'Bilder',
    status: 'Status',
    actions: 'Aktionen',
    loading: 'Projekte werden geladen...',
    error: 'Fehler beim Laden der Projekte',
    retry: 'Erneut versuchen',
    duplicating: 'Projekt wird dupliziert...',
    duplicate: 'Duplizieren',
    duplicateSuccess: 'Projekt erfolgreich dupliziert',
    duplicateFailed: 'Projektduplizierung fehlgeschlagen',
    duplicateTitle: 'Projekt duplizieren',
    duplicateProject: 'Projekt duplizieren',
    duplicateProjectDescription: 'Erstellen Sie eine Kopie dieses Projekts einschließlich aller Bilder. Sie können die Optionen unten anpassen.',
    duplicateCancelled: 'Projektduplizierung abgebrochen',
    duplicatingProject: 'Projekt wird dupliziert',
    duplicatingProjectDescription: 'Ihr Projekt wird dupliziert. Dies kann einige Augenblicke dauern.',
    duplicateProgress: 'Duplizierungsfortschritt',
    duplicationComplete: 'Projektduplizierung abgeschlossen',
    duplicationTaskFetchError: 'Fehler beim Abrufen der Aufgabendaten',
    duplicationCancelError: 'Fehler beim Abbrechen der Duplizierung',
    duplicateProgressDescription: 'Ihr Projekt wird dupliziert. Dieser Vorgang kann bei großen Projekten einige Zeit dauern.',
    duplicationPending: 'Ausstehend',
    duplicationProcessing: 'In Bearbeitung',
    duplicationCompleted: 'Abgeschlossen',
    duplicationFailed: 'Fehlgeschlagen',
    duplicationCancelled: 'Abgebrochen',
    duplicationCancellationFailed: 'Fehler beim Abbrechen der Duplizierung',
    duplicationSuccessMessage: 'Projekt erfolgreich dupliziert! Sie können jetzt auf das neue Projekt zugreifen.',
    copySegmentations: 'Segmentierungsergebnisse kopieren',
    resetImageStatus: 'Bildverarbeitungsstatus zurücksetzen',
    newProjectTitle: 'Neuer Projekttitel',
    itemsProcessed: 'Elemente verarbeitet',
    items: 'Elemente',
    unknownProject: 'Unbekanntes Projekt',
    activeTasks: 'Aktive Aufgaben',
    allTasks: 'Alle Aufgaben',
    noActiveDuplications: 'Keine aktiven Duplizierungen',
    noDuplications: 'Keine Duplizierungsaufgaben gefunden',
    untitledProject: 'Unbenanntes Projekt',
    exportProject: 'Projekt exportieren',
    share: 'Teilen',
    export: 'Exportieren',
    archived: 'Archiviert',
    completed: 'Abgeschlossen',
    draft: 'Entwurf',
    active: 'Aktiv',
  },
  projectToolbar: {
    selectImages: 'Bilder auswählen',
    cancelSelection: 'Auswahl abbrechen',
    export: 'Exportieren',
    uploadImages: 'Bilder hochladen',
  },
  statsOverview: {
    title: 'Dashboard-Übersicht',
    totalProjects: 'Gesamtprojekte',
    totalImages: 'Gesamtbilder',
    completedSegmentations: 'Abgeschlossene Segmentierungen',
    storageUsed: 'Verwendeter Speicherplatz',
    recentActivity: 'Letzte Aktivitäten',
    moreStats: 'Detaillierte Statistiken anzeigen',
    completion: 'Abschlussrate',
    vsLastMonth: 'ggü. Vormonat',
    thisMonth: 'Dieser Monat',
    lastMonth: 'Letzter Monat',
    projectsCreated: 'Erstellte Projekte',
    imagesUploaded: 'Hochgeladene Bilder',
    fetchError: 'Fehler beim Laden der Statistiken',
    storageLimit: 'Speicherlimit',
    activityTitle: 'Letzte Aktivitäten',
    noActivity: 'Keine letzten Aktivitäten',
    activityTypes: {
      project_created: 'Projekt erstellt',
      image_uploaded: 'Bild hochgeladen',
      segmentation_completed: 'Segmentierung abgeschlossen',
    },
  },
  footer: {
    developerName: 'Bc. Michal Průšek',
    facultyName: 'FNSPE CTU in Prag',
    description: 'Fortschrittliche Plattform für Sphäroidsegmentierung und -analyse',
    contactLabel: '<EMAIL>',
    developerLabel: 'Bc. Michal Průšek',
    facultyLabel: 'FNSPE CTU in Prag',
    resourcesTitle: 'Ressourcen',
    documentationLink: 'Dokumentation',
    featuresLink: 'Funktionen',
    tutorialsLink: 'Tutorials',
    researchLink: 'Forschung',
    legalTitle: 'Rechtliche Informationen',
    termsLink: 'Nutzungsbedingungen',
    privacyLink: 'Datenschutzrichtlinie',
    contactUsLink: 'Kontaktieren Sie uns',
    informationTitle: 'Informationen',
    contactTitle: 'Kontakt',
    copyrightNotice: 'SpheroSeg. Alle Rechte vorbehalten.',
    madeWith: 'Erstellt mit',
    by: 'von',
    requestAccessLink: 'Zugriff anfordern',
  },
  features: {
    tag: 'Funktionen',
    title: 'Entdecken Sie die Fähigkeiten unserer Plattform',
    subtitle: 'Fortschrittliche Werkzeuge für die biomedizinische Forschung',
    cards: {
      segmentation: {
        title: 'Fortschrittliche Segmentierung',
        description: 'Präzise Sphäroiderkennung mit Grenzanalyse für genaue Zellmessungen.',
      },
      aiAnalysis: {
        title: 'KI-gestützte Analyse',
        description: 'Nutzen Sie Deep-Learning-Algorithmen für die automatisierte Zellerkennung und -klassifizierung.',
      },
      uploads: {
        title: 'Einfaches Hochladen',
        description: 'Ziehen Sie Ihre Mikroskopiebilder per Drag & Drop für eine sofortige Verarbeitung und Analyse.',
      },
      insights: {
        title: 'Statistische Einblicke',
        description: 'Umfassende Metriken und Visualisierungen zur Gewinnung aussagekräftiger Datenmuster.',
      },
      collaboration: {
        title: 'Team-Zusammenarbeit',
        description: 'Teilen Sie Projekte und Ergebnisse mit Kollegen für eine effizientere Forschung.',
      },
      pipeline: {
        title: 'Automatisierte Pipeline',
        description: 'Optimieren Sie Ihren Workflow mit unseren Stapelverarbeitungswerkzeugen.',
      },
    },
  },
  index: {
    about: {
      tag: 'Über die Plattform',
      title: 'Was ist SpheroSeg?',
      imageAlt: 'Beispiel für Sphäroidsegmentierung',
      paragraph1: 'SpheroSeg ist eine fortschrittliche Plattform, die speziell für die Segmentierung und Analyse von Zellsphäroiden in mikroskopischen Bildern entwickelt wurde.',
      paragraph2: 'Unser Tool kombiniert modernste Algorithmen der künstlichen Intelligenz mit einer intuitiven Benutzeroberfläche, um Forschern präzise Sphäroidgrenzerkennung und Analysefunktionen zu bieten.',
      paragraph3: 'Die Plattform wurde von Michal Průšek von der FNSPE CTU in Prag unter der Betreuung von Adam Novozámský vom UTIA CAS in Zusammenarbeit mit Forschern der Abteilung für Biochemie und Mikrobiologie der UCT Prag entwickelt.',
      contactPrefix: '<EMAIL>',
    },
    cta: {
      title: 'Bereit, Ihre Forschung zu transformieren?',
      subtitle: 'Beginnen Sie noch heute mit SpheroSeg und entdecken Sie neue Möglichkeiten in der Zellsphäroidanalyse.',
      boxTitle: 'Kostenloses Konto erstellen',
      boxText: 'Erhalten Sie Zugriff auf alle Plattformfunktionen und beginnen Sie mit der Analyse Ihrer Mikroskopiebilder.',
      button: 'Konto erstellen',
    },
  },
  tools: {
    zoomIn: 'Vergrößern',
    zoomOut: 'Verkleinern',
    resetView: 'Ansicht zurücksetzen',
    createPolygon: 'Neues Polygon erstellen',
    exitPolygonCreation: 'Polygon-Erstellungsmodus verlassen',
    splitPolygon: 'Polygon in zwei Teile teilen',
    exitSlicingMode: 'Schneidemodus verlassen',
    addPoints: 'Punkte zum Polygon hinzufügen',
    exitPointAddingMode: 'Punkt-Hinzufügungsmodus verlassen',
    undo: 'Rückgängig',
    redo: 'Wiederherstellen',
    save: 'Speichern',
    resegment: 'Neu segmentieren',
    title: 'Werkzeuge',
  },
  segmentation: {
    title: 'Segmentierungseditor',
    resolution: 'Auflösung: {width}x{height}',
    queue: {
      title: 'Segmentierungswarteschlange',
      summary: '{{total}} Aufgaben insgesamt ({{running}} in Bearbeitung, {{queued}} in Warteschlange)',
      noRunningTasks: 'Keine laufenden Aufgaben',
      noQueuedTasks: 'Keine Aufgaben in der Warteschlange',
      task: 'Aufgabe',
      statusRunning: 'Segmentierung: {{count}} laufend{{queued}}',
      statusQueued: ', {{count}} in Warteschlange',
      statusOnlyQueued: 'Segmentierung: {{count}} in Warteschlange',
      statusOnlyQueued_one: 'Segmentierung: 1 in Warteschlange',
      statusOnlyQueued_other: 'Segmentierung: {{count}} in Warteschlange',
      processing: 'In Bearbeitung', // Duplicate, same as below
      queued: 'In Warteschlange',   // Duplicate, same as below
      statusProcessing: 'Segmentierung: {{count}} in Bearbeitung',
      statusReady: 'Segmentierung: Bereit',
      tasksTotal: '{{total}} Aufgaben insgesamt ({{running}} in Bearbeitung, {{queued}} in Warteschlange)'
    },
    selectPolygonForEdit: 'Polygon zum Bearbeiten auswählen',
    selectPolygonForSlice: 'Polygon zum Teilen auswählen',
    selectPolygonForAddPoints: 'Polygon zum Hinzufügen von Punkten auswählen',
    clickToAddPoint: 'Klicken, um einen Punkt hinzuzufügen',
    clickToCompletePolygon: 'Auf den ersten Punkt klicken, um das Polygon zu vervollständigen',
    clickToAddFirstSlicePoint: 'Klicken, um den ersten Schnittpunkt hinzuzufügen',
    clickToAddSecondSlicePoint: 'Klicken, um den zweiten Schnittpunkt hinzuzufügen',
    polygonCreationMode: 'Polygon-Erstellungsmodus',
    polygonEditMode: 'Polygon-Bearbeitungsmodus',
    polygonSliceMode: 'Polygon-Schneidemodus',
    polygonAddPointsMode: 'Punkt-Hinzufügungsmodus',
    viewMode: 'Ansichtsmodus',
    totalPolygons: 'Gesamtpolygone',
    totalVertices: 'Gesamtvertices',
    vertices: 'Vertices',
    zoom: 'Zoom',
    mode: 'Modus',
    selected: 'Ausgewählt',
    none: 'Keine',
    polygons: 'Polygone',
    imageNotFound: 'Bild nicht gefunden',
    returnToProject: 'Zurück zum Projekt',
    backToProject: 'Zurück zum Projekt',
    previousImage: 'Vorheriges Bild',
    nextImage: 'Nächstes Bild',
    toggleShortcuts: 'Kurzbefehle ein-/ausblenden',
    modes: {
      view: 'Ansichtsmodus',
      edit: 'Bearbeitungsmodus',
      create: 'Erstellungsmodus',
      slice: 'Schneidemodus',
      addPoints: 'Punkt-Hinzufügungsmodus',
      deletePolygon: 'Polygon-Löschmodus',
      createPolygon: 'Polygon-Erstellungsmodus',
      editVertices: 'Vertices-Bearbeitungsmodus',
      editMode: 'Bearbeitungsmodus',
      slicingMode: 'Schneidemodus',
      pointAddingMode: 'Punkt-Hinzufügungsmodus',
    },
    status: {
      processing: 'In Bearbeitung',
      queued: 'In Warteschlange',
      completed: 'Abgeschlossen',
      failed: 'Fehlgeschlagen',
    },
    // segmentation.queue is duplicated here in the input. Already translated above.
    notifications: {
      completed: 'Segmentierung erfolgreich abgeschlossen',
      failed: 'Segmentierung fehlgeschlagen: {{error}}',
      queued: 'Segmentierung wurde in die Warteschlange gestellt',
      started: 'Segmentierung wurde gestartet',
    },
    autoSave: {
      enabled: 'Automatisches Speichern: Aktiviert',
      disabled: 'Automatisches Speichern: Deaktiviert',
      idle: 'Automatisches Speichern: Inaktiv',
      pending: 'Ausstehend...',
      saving: 'Wird gespeichert...',
      success: 'Gespeichert',
      error: 'Fehler',
    },
    loading: 'Segmentierung wird geladen...',
    polygon: 'Polygon',
    unsavedChanges: 'Nicht gespeicherte Änderungen',
    noData: 'Keine Segmentierungsdaten verfügbar',
    noPolygons: 'Keine Polygone gefunden',
    regions: 'Segmentierung',
    position: 'Position',
    polygonDeleted: 'Polygon erfolgreich gelöscht',
    saveSuccess: 'Segmentierung erfolgreich gespeichert',
    resegmentSuccess: 'Neusegmentierung erfolgreich gestartet',
    resegmentComplete: 'Neusegmentierung erfolgreich abgeschlossen',
    resegmentError: 'Fehler bei der Neusegmentierung des Bildes',
    resegmentButton: 'Neu segmentieren',
    completedSegmentation: 'Abgeschlossen',
    resegmentButtonTooltip: 'Mit neuronalem Netz neu segmentieren',
    helpTips: {
      title: 'Tipps:',
      edit: {
        createPoint: 'Klicken, um einen neuen Punkt zu erstellen',
        shiftPoints: 'Shift gedrückt halten, um automatisch eine Punktsequenz zu erstellen',
        closePolygon: 'Polygon schließen, indem auf den ersten Punkt geklickt wird',
      },
      slice: {
        start: 'Klicken, um den Schnitt zu beginnen',
        finish: 'Erneut klicken, um den Schnitt zu beenden',
        cancel: 'Esc, um das Schneiden abzubrechen',
      },
      addPoint: {
        hover: 'Über Polygonlinie fahren',
        click: 'Klicken, um Punkt zum ausgewählten Polygon hinzuzufügen',
        exit: 'Esc, um den Hinzufügungsmodus zu verlassen',
      },
    },
  },
  errors: {
    somethingWentWrong: 'Etwas ist schiefgelaufen',
    componentError: 'In dieser Komponente ist ein Fehler aufgetreten',
    goBack: 'Zurückgehen',
    tryAgain: 'Erneut versuchen',
    notFound: 'Seite nicht gefunden',
    unauthorized: 'Unbefugter Zugriff',
    forbidden: 'Zugriff verboten',
    serverError: 'Serverfehler',
    networkError: 'Netzwerkfehler',
    timeoutError: 'Zeitüberschreitung der Anfrage',
    validationError: 'Validierungsfehler',
    unknownError: 'Unbekannter Fehler',
    pageNotFound: 'Seite nicht gefunden',
    pageNotFoundMessage: 'Die angeforderte Seite konnte nicht gefunden werden',
    goHome: 'Zur Startseite gehen',
    fetchSegmentationFailed: 'Fehler beim Abrufen der Segmentierung',
    fetchImageFailed: 'Fehler beim Abrufen des Bildes',
    saveSegmentationFailed: 'Fehler beim Speichern der Segmentierung',
    missingPermissions: 'Unzureichende Berechtigungen',
    invalidInput: 'Ungültige Eingabe',
    resourceNotFound: 'Ressource nicht gefunden',
  },
  settings: {
    title: 'Einstellungen',
    pageTitle: 'Einstellungen',
    profile: 'Profil',
    account: 'Konto',
    appearance: 'Erscheinungsbild',
    notificationsTab: 'Benachrichtigungen',
    profileSettings: 'Profileinstellungen',
    accountSettings: 'Kontoeinstellungen',
    notificationSettings: 'Benachrichtigungseinstellungen',
    securitySettings: 'Sicherheitseinstellungen',
    preferenceSettings: 'Präferenzeinstellungen',
    selectLanguage: 'Sprache auswählen',
    selectTheme: 'Theme auswählen',
    updateProfile: 'Profil aktualisieren',
    changePassword: 'Passwort ändern',
    deleteAccount: 'Konto löschen',
    savedChanges: 'Änderungen erfolgreich gespeichert',
    saveChanges: 'Änderungen speichern',
    profileUpdated: 'Profil erfolgreich aktualisiert',
    languageSettings: 'Spracheinstellungen',
    themeSettings: 'Theme-Einstellungen',
    privacySettings: 'Datenschutzeinstellungen',
    exportData: 'Daten exportieren',
    importData: 'Daten importieren',
    uploadAvatar: 'Profilbild hochladen',
    removeAvatar: 'Profilbild entfernen',
    twoFactorAuth: 'Zwei-Faktor-Authentifizierung',
    emailNotifications: 'E-Mail-Benachrichtigungen',
    pushNotifications: 'Push-Benachrichtigungen',
    weeklyDigest: 'Wöchentliche Zusammenfassung',
    monthlyReport: 'Monatlicher Bericht',
    displaySettings: 'Anzeigeeinstellungen',
    accessibilitySettings: 'Barrierefreiheitseinstellungen',
    advancedSettings: 'Erweiterte Einstellungen',
    useBrowserLanguage: 'Browsersprache verwenden',
    language: 'Sprache',
    theme: 'Theme',
    light: 'Hell',
    dark: 'Dunkel',
    system: 'System',
    languageUpdated: 'Sprache erfolgreich aktualisiert',
    themeUpdated: 'Theme erfolgreich aktualisiert',
    toggleTheme: 'Theme umschalten',
    languageDescription: 'Wählen Sie Ihre bevorzugte Sprache',
    themeDescription: 'Wählen Sie Ihr bevorzugtes Theme',
    profileLoadError: 'Fehler beim Laden des Profils',
    appearanceDescription: 'Passen Sie das Erscheinungsbild der Anwendung an',
    personal: 'Persönliche Informationen',
    fullName: 'Vollständiger Name',
    organization: 'Organisation',
    department: 'Abteilung',
    publicProfile: 'Öffentliches Profil',
    makeProfileVisible: 'Mein Profil für andere Forscher sichtbar machen',
    passwordSettings: 'Passworteinstellungen',
    currentPassword: 'Aktuelles Passwort',
    newPassword: 'Neues Passwort',
    confirmNewPassword: 'Neues Passwort bestätigen',
    dangerZone: 'Gefahrenzone',
    deleteAccountWarning: 'Sobald Sie Ihr Konto löschen, gibt es kein Zurück mehr. Alle Ihre Daten werden dauerhaft gelöscht.',
    savingChanges: 'Änderungen werden gespeichert...',
    notifications: {
      projectUpdates: 'Projektaktualisierungen',
      receiveProjectUpdates: 'Aktualisierungen zu Ihren Projekten erhalten',
      analysisCompleted: 'Analyse abgeschlossen',
      newFeatures: 'Neue Funktionen',
      marketingEmails: 'Marketing-E-Mails',
      billing: 'Rechnungsbenachrichtigungen',
      segmentationResults: 'Segmentierungsergebnisse',
      receiveSegmentationResults: 'Benachrichtigungen erhalten, wenn die Segmentierung abgeschlossen ist',
      newsletterUpdates: 'Newsletter-Aktualisierungen',
      receiveNewsletterUpdates: 'Informationen über neue Funktionen und Updates erhalten',
      collaborationRequests: 'Kollaborationsanfragen',
      receiveCollaborationRequests: 'Benachrichtigungen über Kollaborationsanfragen zu Projekten erhalten',
      commentsMentions: 'Kommentare und Erwähnungen',
      receiveCommentsMentions: 'Benachrichtigungen erhalten, wenn Sie jemand in einem Kommentar erwähnt',
    },
    inAppNotifications: 'In-App-Benachrichtigungen',
    savePreferences: 'Präferenzen speichern',
    notificationSettingsSaved: 'Benachrichtigungseinstellungen gespeichert',
    usernameTaken: 'Dieser Benutzername ist bereits vergeben',
  },
  accessibility: {
    skipToContent: 'Zum Hauptinhalt springen',
  },
  profile: {
    title: 'Titel',
    about: 'Über mich',
    activity: 'Aktivität',
    projects: 'Projekte',
    recentProjects: 'Letzte Projekte',
    recentAnalyses: 'Letzte Analysen',
    accountDetails: 'Kontodetails',
    accountType: 'Kontotyp',
    joinDate: 'Beitrittsdatum',
    lastActive: 'Zuletzt aktiv',
    projectsCreated: 'Erstellte Projekte',
    imagesUploaded: 'Hochgeladene Bilder',
    segmentationsCompleted: 'Abgeschlossene Segmentierungen',
    pageTitle: 'Benutzerprofil',
    editProfile: 'Profil bearbeiten',
    joined: 'Beigetreten am',
    statistics: 'Statistiken',
    images: 'Bilder',
    analyses: 'Analysen',
    storageUsed: 'Verwendeter Speicherplatz',
    recentActivity: 'Letzte Aktivitäten',
    noRecentActivity: 'Keine letzten Aktivitäten',
    fetchError: 'Fehler beim Laden der Profildaten',
    aboutMe: 'Über mich',
    noBio: 'Keine Biografie angegeben',
    avatarHelp: 'Klicken Sie auf das Kamerasymbol, um ein Profilbild hochzuladen',
    avatarImageOnly: 'Bitte wählen Sie eine Bilddatei aus',
    avatarTooLarge: 'Bild muss kleiner als 5MB sein',
    avatarUpdated: 'Profilbild aktualisiert',
    avatarUploadError: 'Fehler beim Hochladen des Profilbilds',
    avatarRemoved: 'Profilbild entfernt',
    avatarRemoveError: 'Fehler beim Entfernen des Profilbilds',
    cropAvatarDescription: 'Passen Sie den Zuschneidebereich an, um Ihr Profilbild festzulegen',
    description: 'Aktualisieren Sie Ihre persönlichen Informationen und Ihr Profilbild',
    saveButton: 'Profil speichern',
    username: 'Benutzername',
    usernamePlaceholder: 'Benutzernamen eingeben',
    fullName: 'Vollständiger Name',
    fullNamePlaceholder: 'Vollständigen Namen eingeben',
    titlePlaceholder: 'z.B. Forscher, Professor',
    organization: 'Organisation',
    organizationPlaceholder: 'Ihre Organisation oder Institution eingeben',
    bio: 'Bio',
    bioPlaceholder: 'Erzählen Sie uns von sich',
    bioDescription: 'Eine kurze Beschreibung über Sie, die in Ihrem Profil sichtbar sein wird',
    location: 'Standort',
    locationPlaceholder: 'Ihren Standort eingeben',
    uploadAvatar: 'Profilbild hochladen',
    removeAvatar: 'Profilbild entfernen',
    cropAvatar: 'Profilbild zuschneiden',
    activityDescription: 'Systemaktivität',
    email: 'E-Mail',
    notProvided: 'Nicht angegeben',
  },
  termsPage: {
    title: 'Nutzungsbedingungen',
    acceptance: {
      title: '1. Annahme der Bedingungen',
      paragraph1: 'Durch den Zugriff auf oder die Nutzung von SpheroSeg erklären Sie sich damit einverstanden, an diese Nutzungsbedingungen und alle geltenden Gesetze und Vorschriften gebunden zu sein. Wenn Sie mit einer dieser Bedingungen nicht einverstanden sind, ist Ihnen die Nutzung dieses Dienstes untersagt.',
    },
    useLicense: {
      title: '2. Nutzungslizenz',
      paragraph1: 'Es wird die Erlaubnis erteilt, SpheroSeg vorübergehend nur für persönliche, nicht-kommerzielle oder akademische Forschungszwecke zu nutzen. Dies ist die Gewährung einer Lizenz, keine Übertragung des Eigentums.',
    },
    dataUsage: {
      title: '3. Datennutzung',
      paragraph1: 'Alle auf SpheroSeg hochgeladenen Daten bleiben Ihr Eigentum. Wir erheben keinen Anspruch auf das Eigentum an Ihren Inhalten, benötigen jedoch bestimmte Berechtigungen, um den Dienst bereitzustellen.',
    },
    limitations: {
      title: '4. Haftungsbeschränkungen',
      paragraph1: 'In keinem Fall haftet SpheroSeg für Schäden, die sich aus der Nutzung oder Unmöglichkeit der Nutzung der Plattform ergeben, selbst wenn wir auf die Möglichkeit solcher Schäden hingewiesen wurden.',
    },
    revisions: {
      title: '5. Überarbeitungen und Fehler',
      paragraph1: 'Die auf SpheroSeg erscheinenden Materialien können technische, typografische oder fotografische Fehler enthalten. Wir garantieren nicht, dass die Materialien korrekt, vollständig oder aktuell sind.',
    },
    governingLaw: {
      title: '6. Geltendes Recht',
      paragraph1: 'Diese Bedingungen unterliegen den Gesetzen des Landes, in dem der Dienst gehostet wird, und werden entsprechend ausgelegt, und Sie unterwerfen sich unwiderruflich der ausschließlichen Zuständigkeit der Gerichte an diesem Ort.',
    },
  },
  privacyPage: {
    title: 'Datenschutzrichtlinie',
    introduction: {
      title: '1. Einführung',
      paragraph1: 'Diese Datenschutzrichtlinie erklärt, wie SpheroSeg ("wir", "uns", "unser") Ihre Informationen sammelt, verwendet und weitergibt, wenn Sie unsere Plattform zur Sphäroidsegmentierung und -analyse nutzen.',
    },
    informationWeCollect: {
      title: '2. Informationen, die wir sammeln',
      paragraph1: 'Wir sammeln Informationen, die Sie uns direkt zur Verfügung stellen, wenn Sie ein Konto erstellen, Bilder hochladen, Projekte erstellen und anderweitig mit unseren Diensten interagieren.',
    },
    personalInformation: {
      title: '2.1 Persönliche Informationen',
      paragraph1: 'Dazu gehören Ihr Name, Ihre E-Mail-Adresse, Ihre Institution/Organisation und andere Informationen, die Sie bei der Erstellung eines Kontos oder der Anforderung von Zugriff auf unsere Dienste angeben.',
    },
    researchData: {
      title: '2.2 Forschungsdaten',
      paragraph1: 'Dazu gehören Bilder, die Sie hochladen, Projektdetails, Analyseergebnisse und andere forschungsbezogene Daten, die Sie auf unserer Plattform erstellen oder hochladen.',
    },
    usageInformation: {
      title: '2.3 Nutzungsinformationen',
      paragraph1: 'Wir sammeln Informationen darüber, wie Sie unsere Plattform nutzen, einschließlich Protokolldaten, Geräteinformationen und Nutzungsmuster.',
    },
    howWeUse: {
      title: '3. Wie wir Ihre Informationen verwenden',
      paragraph1: 'Wir verwenden die von uns gesammelten Informationen, um unsere Dienste bereitzustellen, zu warten und zu verbessern, um mit Ihnen zu kommunizieren und um unseren gesetzlichen Verpflichtungen nachzukommen.',
    },
    dataSecurity: {
      title: '4. Datensicherheit',
      paragraph1: 'Wir ergreifen geeignete Sicherheitsmaßnahmen, um Ihre persönlichen Informationen und Forschungsdaten vor unbefugtem Zugriff, Änderung, Offenlegung oder Zerstörung zu schützen.',
    },
    dataSharing: {
      title: '5. Datenweitergabe',
      paragraph1: 'Wir verkaufen Ihre persönlichen Informationen oder Forschungsdaten nicht. Wir können Ihre Informationen unter begrenzten Umständen weitergeben, z. B. mit Ihrer Zustimmung, zur Erfüllung gesetzlicher Verpflichtungen oder an Dienstleister, die uns beim Betrieb unserer Plattform helfen.',
    },
    yourChoices: {
      title: '6. Ihre Wahlmöglichkeiten',
      paragraph1: 'Sie können Ihre Kontoinformationen und Forschungsdaten über Ihre Kontoeinstellungen einsehen, aktualisieren oder löschen. Sie können uns auch kontaktieren, um Zugriff, Berichtigung oder Löschung Ihrer bei uns gespeicherten persönlichen Daten zu beantragen.',
    },
    changes: {
      title: '7. Änderungen dieser Richtlinie',
      paragraph1: 'Wir können diese Datenschutzrichtlinie von Zeit zu Zeit aktualisieren. Wir werden Sie über Änderungen informieren, indem wir die neue Datenschutzrichtlinie auf dieser Seite veröffentlichen und das Datum "Zuletzt aktualisiert" aktualisieren.',
    },
    contactUs: {
      title: '8. Kontaktieren Sie uns',
      paragraph1: 'Wenn Sie Fragen zu dieser Datenschutzrichtlinie haben, kontaktieren Sie uns <NAME_EMAIL>.',
    },
    lastUpdated: 'Zuletzt aktualisiert: 1. Juli 2023',
  },
  shortcuts: {
    button: 'Kurzbefehle',
    editMode: 'In Bearbeitungsmodus wechseln',
    sliceMode: 'In Schneidemodus wechseln',
    addPointMode: 'In Punkt-Hinzufügungsmodus wechseln',
    holdShift: 'Shift gedrückt halten, um Punkte automatisch hinzuzufügen (im Bearbeitungsmodus)',
    undo: 'Rückgängig',
    redo: 'Wiederherstellen',
    deletePolygon: 'Ausgewähltes Polygon löschen',
    cancel: 'Aktuelle Operation abbrechen',
    zoomIn: 'Vergrößern',
    zoomOut: 'Verkleinern',
    resetView: 'Ansicht zurücksetzen',
    title: 'Tastaturkurzbefehle',
    viewMode: 'Ansichtsmodus',
    editVerticesMode: 'Vertices-Bearbeitungsmodus',
    addPointsMode: 'Punkt-Hinzufügungsmodus',
    createPolygonMode: 'Polygon-Erstellungsmodus',
    save: 'Speichern',
    description: 'Diese Kurzbefehle funktionieren im Segmentierungseditor für schnelleres und komfortableres Arbeiten.',
  },
  imageProcessor: {
    segmentationStarted: 'Segmentierungsprozess wurde gestartet...',
    startSegmentationTooltip: 'Segmentierung starten',
    processingTooltip: 'Verarbeite...',
    savingTooltip: 'Wird gespeichert...',
    completedTooltip: 'Segmentierung abgeschlossen',
    retryTooltip: 'Segmentierung erneut versuchen',
  },
  uploader: {
    dragDrop: 'Bilder hierher ziehen oder klicken, um Dateien auszuwählen',
    dropFiles: 'Dateien hier ablegen...',
    segmentAfterUploadLabel: 'Bilder sofort nach dem Hochladen segmentieren',
    filesToUpload: 'Hochzuladende Dateien',
    uploadBtn: 'Hochladen',
    uploadError: 'Während des Hochladens ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.',
    clickToUpload: 'Klicken, um Dateien zu durchsuchen',
    selectProjectLabel: 'Projekt auswählen',
    selectProjectPlaceholder: 'Projekt auswählen...',
    noProjectsFound: 'Keine Projekte gefunden. Erstellen Sie zuerst ein neues.',
    imageOnly: '(Nur Bilddateien)',
    uploadingImages: 'Bilder werden hochgeladen...',
    uploadComplete: 'Upload abgeschlossen',
    uploadFailed: 'Upload fehlgeschlagen',
    processingImages: 'Bilder werden verarbeitet...',
    dragAndDropFiles: 'Dateien hierher ziehen und ablegen',
    or: 'oder',
    clickToSelect: 'Klicken, um Dateien auszuwählen',
  },
  images: {
    uploadImages: 'Bilder hochladen',
    dragDrop: 'Bilder hierher ziehen und ablegen',
    clickToSelect: 'oder klicken, um Dateien auszuwählen',
    acceptedFormats: 'Unterstützte Formate: JPEG, PNG, TIFF, BMP (max. 10MB)',
    uploadProgress: 'Upload-Fortschritt',
    uploadingTo: 'Hochladen nach',
    currentProject: 'Aktuelles Projekt',
    autoSegment: 'Bilder nach dem Hochladen automatisch segmentieren',
    uploadCompleted: 'Upload abgeschlossen',
    uploadFailed: 'Upload fehlgeschlagen',
    imagesUploaded: 'Bilder erfolgreich hochgeladen',
    imagesFailed: 'Bild-Upload fehlgeschlagen',
    viewAnalyses: 'Analysen anzeigen',
    noAnalysesYet: 'Noch keine Analysen',
    runAnalysis: 'Analyse durchführen',
    viewResults: 'Ergebnisse anzeigen',
    dropImagesHere: 'Bilder hier ablegen...',
    selectProjectFirst: 'Bitte wählen Sie zuerst ein Projekt aus',
    projectRequired: 'Sie müssen ein Projekt auswählen, bevor Sie Bilder hochladen können',
    imageOnly: '(Nur Bilddateien)',
    dropFiles: 'Dateien hier ablegen...',
    filesToUpload: 'Hochzuladende Dateien ({{count}})',
    uploadBtn: '{{count}} Bilder hochladen',
    uploadError: 'Während des Hochladens ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.',
    noProjectsToUpload: 'Keine Projekte zum Hochladen verfügbar. Erstellen Sie zuerst ein Projekt.',
    notFound: 'Projekt "{{projectName}}" nicht gefunden. Es wurde möglicherweise gelöscht.',
  },
  export: {
    formatDescriptions: {
      COCO: "Common Objects in Context (COCO) JSON-Format für Objekterkennung",
      YOLO: "You Only Look Once (YOLO) Textformat für Objekterkennung",
      MASK: "Binäre Maskenbilder für jedes segmentierte Objekt",
      POLYGONS: "Polygonkoordinaten im JSON-Format"
    },
    exportCompleted: 'Export abgeschlossen',
    exportFailed: 'Export fehlgeschlagen',
    title: 'Segmentierungsdaten exportieren',
    spheroidMetrics: 'Sphäroidmetriken',
    visualization: 'Visualisierung',
    cocoFormat: 'COCO-Format',
    close: 'Schließen',
    metricsExported: 'Metriken erfolgreich exportiert',
    options: {
      includeMetadata: 'Metadaten einschließen',
      includeSegmentation: 'Segmentierung einschließen',
      selectExportFormat: 'Exportformat auswählen',
      includeObjectMetrics: 'Objektmetriken einschließen',
      selectMetricsFormat: 'Metrikformat auswählen',
      metricsFormatDescription: {
        EXCEL: 'Excel-Datei (.xlsx)',
        CSV: 'CSV-Datei (.csv)',
      },
      includeImages: 'Originalbilder einschließen',
      exportMetricsOnly: 'Nur Metriken exportieren',
      metricsRequireSegmentation: 'Der Export von Metriken erfordert eine abgeschlossene Segmentierung',
    },
    formats: {
      COCO: "COCO JSON",
      YOLO: "YOLO TXT",
      MASK: 'Maske (TIFF)',
      POLYGONS: 'Polygone (JSON)',
    },
    metricsFormats: {
      EXCEL: 'Excel (.xlsx)',
      CSV: 'CSV (.csv)',
    },
    selectImagesForExport: 'Bilder für den Export auswählen',
  },
  metrics: {
    area: 'Fläche',
    perimeter: 'Umfang',
    circularity: 'Zirkularität',
    sphericity: 'Sphärizität',
    solidity: 'Solidität',
    compactness: 'Kompaktheit',
    convexity: 'Konvexität',
    visualization: 'Metrik-Visualisierung',
    visualizationHelp: 'Visuelle Darstellung der Metriken für alle Sphäroide in diesem Bild',
    barChart: 'Balkendiagramm',
    pieChart: 'Kreisdiagramm',
    comparisonChart: 'Vergleichsdiagramm',
    keyMetricsComparison: 'Vergleich der Schlüsselmetriken',
    areaDistribution: 'Flächenverteilung',
    shapeMetricsComparison: 'Vergleich der Formmetriken',
    noPolygonsFound: 'Keine Polygone für die Analyse gefunden',
  },
  imageStatus: {
    completed: 'Verarbeitet',
    processing: 'In Bearbeitung',
    pending: 'Ausstehend',
    failed: 'Fehlgeschlagen',
    noImage: 'Kein Bild',
    untitledImage: 'Unbenanntes Bild',
  },
  projectActions: {
    duplicateTooltip: 'Projekt duplizieren',
    deleteTooltip: 'Projekt löschen',
    deleteConfirmTitle: 'Sind Sie sicher?',
    deleteConfirmDesc: 'Sind Sie sicher, dass Sie das Projekt "{{projectName}}" löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.',
    deleteSuccess: 'Projekt "{{projectName}}" wurde erfolgreich gelöscht.',
    deleteError: 'Projektlöschung fehlgeschlagen.',
    duplicateSuccess: 'Projekt "{{projectName}}" wurde erfolgreich dupliziert.',
    duplicateError: 'Projektduplizierung fehlgeschlagen.',
    makePrivateTooltip: 'Als privat markieren',
    makePublicTooltip: 'Als öffentlich markieren',
    shareTooltip: 'Projekt teilen',
    downloadTooltip: 'Projekt herunterladen',
    notFound: 'Projekt "{{projectName}}" nicht gefunden. Es wurde möglicherweise bereits gelöscht.',
  },
  editor: {
    backButtonTooltip: 'Zurück zur Projektübersicht',
    exportButtonTooltip: 'Aktuelle Segmentierungsdaten exportieren',
    saveTooltip: 'Änderungen speichern',
    image: 'Bild',
    previousImage: 'Vorheriges Bild',
    nextImage: 'Nächstes Bild',
    resegmentButton: 'Neu segmentieren',
    resegmentButtonTooltip: 'Segmentierung für dieses Bild erneut ausführen',
    exportMaskButton: 'Maske exportieren',
    exportMaskButtonTooltip: 'Segmentierungsmaske für dieses Bild exportieren',
    backButton: 'Zurück',
    exportButton: 'Exportieren',
    saveButton: 'Speichern',
    loadingProject: 'Projekt wird geladen...',
    loadingImage: 'Bild wird geladen...',
    sliceErrorInvalidPolygon: 'Teilen nicht möglich: Ungültiges Polygon ausgewählt.',
    sliceWarningInvalidResult: 'Das Teilen hat zu kleine und ungültige Polygone erzeugt.',
    sliceWarningInvalidIntersections: 'Ungültiger Schnitt: Die Schnittlinie muss das Polygon an genau zwei Punkten schneiden.',
    sliceSuccess: 'Polygon erfolgreich geteilt.',
    noPolygonToSlice: 'Keine Polygone zum Teilen verfügbar.',
    savingTooltip: 'Wird gespeichert...',
  },
  segmentationPage: {
    noImageSelected: 'Kein Bild für die Neusegmentierung ausgewählt.',
    resegmentationStarted: 'Neusegmentierung mit ResUNet-Neuronalem Netzwerk wird gestartet...',
    resegmentationQueued: 'Neusegmentierung wurde in die Warteschlange gestellt.',
    resegmentationCompleted: 'Neusegmentierung erfolgreich abgeschlossen.',
    resegmentationFailed: 'Neusegmentierung fehlgeschlagen.',
    resegmentationTimeout: 'Zeitüberschreitung bei der Neusegmentierung. Überprüfen Sie den Warteschlangenstatus.',
    resegmentationError: 'Fehler beim Starten der Neusegmentierung.',
    resegmentTooltip: 'Neu segmentieren',
  }
}