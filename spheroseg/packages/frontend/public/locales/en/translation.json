{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "upload": "Upload", "download": "Download", "export": "Export", "import": "Import", "yes": "Yes", "no": "No", "ok": "OK", "confirm": "Confirm", "retry": "Retry", "imageNotFound": "Image not found", "returnToProject": "Return to project", "uploadImages": "Upload Images"}, "auth": {"signIn": "Sign In", "signOut": "Sign Out", "signUp": "Sign Up", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "username": "Username", "rememberMe": "Remember Me", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed", "logoutSuccess": "Logout successful", "logoutError": "Logout failed", "registerSuccess": "Registration successful", "registerError": "Registration failed", "passwordResetSuccess": "Password reset successful", "passwordResetError": "Password reset failed"}, "project": {"title": "Project", "projects": "Projects", "createProject": "Create Project", "editProject": "Edit Project", "deleteProject": "Delete Project", "projectName": "Project Name", "projectDescription": "Project Description", "projectCreatedAt": "Created At", "projectUpdatedAt": "Updated At", "projectOwner": "Owner", "projectMembers": "Members", "projectSettings": "Settings", "projectImages": "Images", "projectSegmentations": "Segmentations", "projectExports": "Exports", "projectImports": "Imports", "projectStats": "Statistics", "projectActivity": "Activity", "projectLogs": "Logs", "projectTasks": "Tasks", "projectQueue": "Queue", "projectStatus": "Status", "projectProgress": "Progress", "projectCompletion": "Completion", "projectDuration": "Duration", "projectDeadline": "Deadline", "projectPriority": "Priority", "projectTags": "Tags", "projectCategory": "Category", "projectType": "Type", "projectVisibility": "Visibility", "projectAccess": "Access", "projectPermissions": "Permissions", "projectRoles": "Roles", "projectInvitations": "Invitations", "projectRequests": "Requests", "projectNotifications": "Notifications", "projectAlerts": "<PERSON><PERSON><PERSON>", "projectWarnings": "Warnings", "projectErrors": "Errors", "projectSuccess": "Success", "projectInfo": "Info", "projectHelp": "Help", "projectSupport": "Support", "projectFeedback": "<PERSON><PERSON><PERSON>", "projectRating": "Rating", "projectReviews": "Reviews", "projectComments": "Comments", "projectNotes": "Notes", "projectDocumentation": "Documentation", "projectGuides": "Guides", "projectTutorials": "Tutorials", "projectExamples": "Examples", "projectTemplates": "Templates", "projectPresets": "Presets", "projectDefaults": "De<PERSON>ults", "projectCustomization": "Customization", "projectThemes": "Themes", "projectStyles": "Styles", "projectLayouts": "Layouts", "projectViews": "Views", "projectDashboard": "Dashboard", "projectOverview": "Overview", "projectSummary": "Summary", "projectDetails": "Details", "projectHistory": "History", "projectTimeline": "Timeline", "projectCalendar": "Calendar", "projectSchedule": "Schedule", "projectPlanning": "Planning", "projectForecasting": "Forecasting", "projectPredictions": "Predictions", "projectAnalytics": "Analytics", "projectReports": "Reports", "projectCharts": "Charts", "projectGraphs": "Graphs", "projectTables": "Tables", "projectLists": "Lists", "projectGrids": "Grids", "projectCards": "Cards", "projectItems": "Items", "projectElements": "Elements", "projectComponents": "Components", "projectModules": "<PERSON><PERSON><PERSON>", "projectSections": "Sections", "projectPages": "Pages", "projectScreens": "Screens", "projectWindows": "Windows", "projectDialogs": "Dialogs", "projectModals": "Modals", "projectPopups": "Popups", "projectTooltips": "Tooltips", "projectHints": "Hints", "projectMessages": "Messages", "noImages": {"title": "No Images Yet", "description": "This project doesn't have any images yet. Upload images to get started with segmentation.", "uploadButton": "Upload Images"}, "errorLoading": "Error loading project"}, "segmentation": {"imageNotFound": "Image not found", "returnToProject": "Return to project", "editor": "Segmentation Editor", "save": "Save Segmentation", "undo": "Undo", "redo": "Redo", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "resetView": "Reset View", "editMode": "Edit Mode", "viewMode": "View Mode", "addPolygon": "Add Polygon", "deletePolygon": "Delete Polygon", "editPolygon": "Edit Polygon", "resegment": "Re-segment", "loading": "Loading segmentation...", "saving": "Saving segmentation...", "saveSuccess": "Segmentation saved successfully", "saveError": "Failed to save segmentation", "loadError": "Failed to load segmentation", "resegmentSuccess": "Re-segmentation triggered successfully", "resegmentError": "Failed to trigger re-segmentation"}}