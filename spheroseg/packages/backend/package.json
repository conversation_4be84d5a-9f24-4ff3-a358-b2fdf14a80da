{"name": "@spheroseg/backend", "version": "1.0.0", "license": "MIT", "main": "dist/server.js", "scripts": {"build": "tsc --skip<PERSON><PERSON>Check && tsc-alias", "start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only --watch src --ignore-watch node_modules --notify --clear -r tsconfig-paths/register src/server.ts", "dev:legacy": "ts-node-dev --respawn --transpile-only --watch src --ignore-watch node_modules --notify --clear -r tsconfig-paths/register src/simple-server.js", "migrate": "echo 'Migration skipped in development mode'", "migrate:create": "echo 'Migration creation skipped in development mode'", "migrate:up": "echo 'Migration up skipped in development mode'", "migrate:down": "echo 'Migration down skipped in development mode'", "update-file-sizes": "node src/scripts/update-file-sizes.js", "test": "jest", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --reporters=default --reporters=junit", "clean": "rimraf dist node_modules .turbo", "lint": "eslint src --ext .ts", "lint:fix": "npx eslint --version && npx eslint src --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\"", "code:check": "npm run lint && npm run format:check", "code:fix": "npm run lint:fix && npm run format"}, "dependencies": {"@spheroseg/types": "file:../types", "@types/helmet": "^4.0.0", "@types/node-fetch": "^2.6.12", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "canvas": "^3.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csrf": "^3.1.0", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "express": "^4.17.1", "express-csp-header": "^6.1.0", "express-rate-limit": "^7.5.0", "file-saver": "^2.0.5", "helmet": "^7.2.0", "jsonwebtoken": "^9.0.0", "jszip": "^3.10.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "node-fetch": "^2.7.0", "node-pg-migrate": "^6.2.2", "nodemailer": "^7.0.2", "pg": "^8.15.6", "prom-client": "^15.1.3", "rate-limit-redis": "^4.2.0", "rate-limiter-flexible": "^5.0.0", "sharp": "^0.34.1", "socket.io": "^4.7.5", "winston": "^3.17.0", "zod": "^3.24.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/dotenv": "^6.1.1", "@types/express": "^4.17.1", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/jszip": "^3.4.0", "@types/multer": "^1.4.12", "@types/node": "^18.11.9", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.11.10", "@types/supertest": "^6.0.3", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.57.1", "jest": "^29.7.0", "pg-mem": "^2.8.1", "prettier": "^3.2.5", "rimraf": "^5.0.5", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-node-dev": "^2.0.0", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "typescript": "^5.0.0", "uuid": "^10.0.0"}, "keywords": [], "author": "", "types": "./dist/server.d.ts", "description": ""}